#!/bin/bash

# PROTON Mail Bridge Installation Script
# =====================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BRIDGE_VERSION="3.21.1"
BRIDGE_DEB_URL="https://proton.me/download/bridge/protonmail-bridge_${BRIDGE_VERSION}-1_amd64.deb"
BRIDGE_DEB_FILE="/tmp/protonmail-bridge_${BRIDGE_VERSION}-1_amd64.deb"

echo "🚀 PROTON Mail Bridge Installation für nk-it.cloud"
echo "=================================================="

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/install.log"
}

# Systemvoraussetzungen prüfen
check_requirements() {
    log "Prüfe Systemvoraussetzungen..."
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log "⚠️  Warnung: Läuft als root. Empfohlen wird ein separater Benutzer."
    fi
    
    # Check available space
    AVAILABLE_SPACE=$(df /opt --output=avail | tail -n1)
    if [[ $AVAILABLE_SPACE -lt 1048576 ]]; then  # 1GB in KB
        log "❌ Fehler: Nicht genügend Speicherplatz verfügbar (mindestens 1GB erforderlich)"
        exit 1
    fi
    
    # Check internet connectivity
    if ! ping -c 1 proton.me &> /dev/null; then
        log "❌ Fehler: Keine Internetverbindung zu proton.me"
        exit 1
    fi
    
    log "✅ Systemvoraussetzungen erfüllt"
}

# Dependencies installieren
install_dependencies() {
    log "Installiere Abhängigkeiten..."
    
    apt-get update
    apt-get install -y \
        wget \
        curl \
        gnupg \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        docker.io \
        docker-compose \
        netcat-openbsd \
        openssl
    
    # Docker starten und aktivieren
    systemctl start docker
    systemctl enable docker
    
    log "✅ Abhängigkeiten installiert"
}

# PROTON Bridge herunterladen
download_bridge() {
    log "Lade PROTON Mail Bridge v${BRIDGE_VERSION} herunter..."
    
    if [[ -f "$BRIDGE_DEB_FILE" ]]; then
        log "Bridge-Paket bereits vorhanden, überspringe Download"
        return
    fi
    
    wget -O "$BRIDGE_DEB_FILE" "$BRIDGE_DEB_URL"
    
    if [[ ! -f "$BRIDGE_DEB_FILE" ]]; then
        log "❌ Fehler: Download fehlgeschlagen"
        exit 1
    fi
    
    log "✅ Bridge heruntergeladen: $BRIDGE_DEB_FILE"
}

# PROTON Bridge installieren
install_bridge() {
    log "Installiere PROTON Mail Bridge..."
    
    # Install the .deb package
    dpkg -i "$BRIDGE_DEB_FILE" || apt-get install -f -y
    
    log "✅ PROTON Mail Bridge installiert"
}

# Verzeichnisse und Berechtigungen einrichten
setup_directories() {
    log "Richte Verzeichnisse ein..."
    
    # Erstelle notwendige Verzeichnisse
    mkdir -p "$PROJECT_DIR"/{data,config,logs,scripts,tests}
    mkdir -p "$PROJECT_DIR/data"/{cache,keychain,backup}
    mkdir -p "$PROJECT_DIR/config/certs"
    
    # Setze Berechtigungen
    chmod 755 "$PROJECT_DIR"
    chmod 700 "$PROJECT_DIR/data/keychain"
    chmod 755 "$PROJECT_DIR/logs"
    chmod +x "$PROJECT_DIR/scripts"/*.sh
    
    log "✅ Verzeichnisse eingerichtet"
}

# SSL-Zertifikate generieren
generate_certificates() {
    log "Generiere SSL-Zertifikate..."
    
    CERT_DIR="$PROJECT_DIR/config/certs"
    
    if [[ -f "$CERT_DIR/bridge.crt" ]]; then
        log "Zertifikate bereits vorhanden, überspringe Generierung"
        return
    fi
    
    # Generiere private key
    openssl genrsa -out "$CERT_DIR/bridge.key" 2048
    
    # Generiere certificate
    openssl req -new -x509 -key "$CERT_DIR/bridge.key" -out "$CERT_DIR/bridge.crt" -days 365 -subj "/C=DE/ST=NRW/L=City/O=nk-it.cloud/CN=mailbridge.nk-it.cloud"
    
    # Setze Berechtigungen
    chmod 600 "$CERT_DIR/bridge.key"
    chmod 644 "$CERT_DIR/bridge.crt"
    
    log "✅ SSL-Zertifikate generiert"
}

# Hauptinstallation
main() {
    log "Starte PROTON Mail Bridge Installation..."
    
    check_requirements
    install_dependencies
    download_bridge
    install_bridge
    setup_directories
    generate_certificates
    
    log "🎉 Installation abgeschlossen!"
    log "Nächste Schritte:"
    log "1. Bearbeite .env Datei mit deinen PROTON Zugangsdaten"
    log "2. Führe ./scripts/setup-auth.sh aus für die Authentifizierung"
    log "3. Starte den Service mit ./scripts/start-bridge.sh"
    log "4. Teste die Verbindung mit ./tests/test-connection.sh"
}

# Cleanup bei Fehler
cleanup() {
    log "Cleanup nach Fehler..."
    [[ -f "$BRIDGE_DEB_FILE" ]] && rm -f "$BRIDGE_DEB_FILE"
}

trap cleanup ERR

# Script ausführen
main "$@"
