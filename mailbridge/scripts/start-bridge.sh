#!/bin/bash

# PROTON Mail Bridge Service Starter
# ==================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Lade Umgebungsvariablen
if [[ -f "$PROJECT_DIR/.env" ]]; then
    source "$PROJECT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🚀 PROTON Mail Bridge Service Starter"
echo "===================================="

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/service.log"
}

# Prüfe ob Bridge bereits läuft
check_if_running() {
    if pgrep -f "protonmail-bridge" > /dev/null; then
        log "⚠️  PROTON Bridge läuft bereits"
        read -p "Möchtest du den Service neu starten? (y/N): " restart
        if [[ "$restart" =~ ^[Yy]$ ]]; then
            stop_bridge
        else
            log "Service läuft bereits. Beende."
            exit 0
        fi
    fi
}

# Bridge stoppen
stop_bridge() {
    log "Stoppe PROTON Bridge..."
    pkill -f "protonmail-bridge" || true
    sleep 3
    log "✅ Bridge gestoppt"
}

# Docker Compose starten
start_docker_compose() {
    log "Starte Bridge mit Docker Compose..."
    
    cd "$PROJECT_DIR"
    
    # Prüfe ob Docker läuft
    if ! systemctl is-active --quiet docker; then
        log "Starte Docker Service..."
        systemctl start docker
    fi
    
    # Starte Bridge Container
    docker compose up -d proton-bridge
    
    if [[ $? -eq 0 ]]; then
        log "✅ Bridge Container gestartet"
    else
        log "❌ Fehler beim Starten des Bridge Containers"
        exit 1
    fi
}

# Native Bridge starten
start_native_bridge() {
    log "Starte Bridge nativ..."
    
    # Setze Umgebungsvariablen
    export BRIDGE_LOG_LEVEL="$BRIDGE_LOG_LEVEL"
    export BRIDGE_CACHE_DIR="$BRIDGE_CACHE_DIR"
    export BRIDGE_CONFIG_DIR="$BRIDGE_CONFIG_DIR"
    
    # Starte Bridge im Hintergrund
    nohup protonmail-bridge \
        --log-level="$BRIDGE_LOG_LEVEL" \
        --cache-dir="$BRIDGE_CACHE_DIR" \
        --config-dir="$BRIDGE_CONFIG_DIR" \
        > "$PROJECT_DIR/logs/bridge.log" 2>&1 &
    
    BRIDGE_PID=$!
    echo $BRIDGE_PID > "$PROJECT_DIR/data/bridge.pid"
    
    log "✅ Bridge gestartet (PID: $BRIDGE_PID)"
}

# Warte auf Bridge Bereitschaft
wait_for_bridge() {
    log "Warte auf Bridge Bereitschaft..."
    
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if nc -z localhost "$BRIDGE_IMAP_PORT" && nc -z localhost "$BRIDGE_SMTP_PORT"; then
            log "✅ Bridge ist bereit (IMAP: $BRIDGE_IMAP_PORT, SMTP: $BRIDGE_SMTP_PORT)"
            return 0
        fi
        
        attempt=$((attempt + 1))
        log "Warte auf Bridge... ($attempt/$max_attempts)"
        sleep 2
    done
    
    log "❌ Bridge ist nach $max_attempts Versuchen nicht bereit"
    return 1
}

# Service Status prüfen
check_service_status() {
    log "Prüfe Service Status..."
    
    # Prüfe Prozess
    if pgrep -f "protonmail-bridge" > /dev/null; then
        log "✅ Bridge Prozess läuft"
    else
        log "❌ Bridge Prozess läuft nicht"
        return 1
    fi
    
    # Prüfe Ports
    if nc -z localhost "$BRIDGE_IMAP_PORT"; then
        log "✅ IMAP Port $BRIDGE_IMAP_PORT aktiv"
    else
        log "❌ IMAP Port $BRIDGE_IMAP_PORT nicht erreichbar"
    fi
    
    if nc -z localhost "$BRIDGE_SMTP_PORT"; then
        log "✅ SMTP Port $BRIDGE_SMTP_PORT aktiv"
    else
        log "❌ SMTP Port $BRIDGE_SMTP_PORT nicht erreichbar"
    fi
    
    # Prüfe Docker Container (falls verwendet)
    if docker ps | grep -q "proton-bridge"; then
        log "✅ Docker Container läuft"
    fi
}

# Firewall Regeln einrichten
setup_firewall() {
    log "Richte Firewall-Regeln ein..."
    
    # Erlaube IMAP und SMTP Ports
    ufw allow from *********/24 to any port "$BRIDGE_IMAP_PORT" comment "PROTON Bridge IMAP"
    ufw allow from *********/24 to any port "$BRIDGE_SMTP_PORT" comment "PROTON Bridge SMTP"
    
    log "✅ Firewall-Regeln eingerichtet"
}

# Hauptfunktion
main() {
    log "Starte PROTON Mail Bridge Service..."
    
    check_if_running
    
    # Wähle Startmethode
    if [[ -f "$PROJECT_DIR/docker-compose.yml" ]] && (command -v "docker compose" &> /dev/null || command -v docker-compose &> /dev/null); then
        start_docker_compose
    else
        start_native_bridge
    fi
    
    wait_for_bridge
    check_service_status
    setup_firewall
    
    log "🎉 PROTON Mail Bridge Service erfolgreich gestartet!"
    log ""
    log "Service Details:"
    log "IMAP Server: $CONTAINER_IP:$BRIDGE_IMAP_PORT"
    log "SMTP Server: $CONTAINER_IP:$BRIDGE_SMTP_PORT"
    log "Log Datei: $PROJECT_DIR/logs/bridge.log"
    log ""
    log "Nächste Schritte:"
    log "1. Teste die Verbindung: ./tests/test-connection.sh"
    log "2. Konfiguriere deine Mail-Clients mit den obigen Einstellungen"
}

# Script ausführen
main "$@"
