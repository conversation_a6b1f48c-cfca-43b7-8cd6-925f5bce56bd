#!/bin/bash

# PROTON Mail Bridge Native Installation (Proxmox LXC Variante)
# =============================================================
# Basiert auf der Reddit-Anleitung für headless Setup

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Lade Umgebungsvariablen
if [[ -f "$PROJECT_DIR/.env" ]]; then
    source "$PROJECT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🔧 PROTON Mail Bridge Native Installation"
echo "========================================"
echo "Proxmox LXC Container Setup"
echo "Container IP: $CONTAINER_IP"
echo ""

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/native-install.log"
}

# System-Updates und Abhängigkeiten
install_dependencies() {
    log "Installiere System-Updates und Abhängigkeiten..."
    
    apt-get update
    apt-get -y upgrade
    
    # Grundlegende Pakete
    apt-get -y install \
        vim \
        nano \
        screen \
        gnupg \
        pass \
        tcpdump \
        socat \
        curl \
        wget \
        netcat-openbsd \
        ufw \
        systemd
    
    log "✅ Abhängigkeiten installiert"
}

# PROTON Bridge herunterladen und installieren
download_and_install_bridge() {
    log "Lade PROTON Bridge herunter..."
    
    local bridge_version="3.21.1"
    local bridge_url="https://proton.me/download/bridge/protonmail-bridge_${bridge_version}-1_amd64.deb"
    local bridge_file="/tmp/protonmail-bridge_${bridge_version}-1_amd64.deb"
    
    # Download
    curl -o "$bridge_file" "$bridge_url"
    
    if [[ ! -f "$bridge_file" ]]; then
        log "❌ Download fehlgeschlagen"
        exit 1
    fi
    
    # Installation
    log "Installiere PROTON Bridge..."
    apt-get -y install "$bridge_file"
    
    # Cleanup
    rm -f "$bridge_file"
    
    log "✅ PROTON Bridge installiert"
}

# GPG und Pass Setup
setup_gpg_and_pass() {
    log "Richte GPG und Pass ein..."
    
    # Prüfe ob GPG Key bereits existiert
    if gpg --list-secret-keys | grep -q "$PROTON_USERNAME"; then
        log "GPG Key für $PROTON_USERNAME bereits vorhanden"
        return 0
    fi
    
    # Erstelle GPG Key automatisch
    cat > "/tmp/gpg-batch" << EOF
%echo Generating GPG key for PROTON Bridge
Key-Type: RSA
Key-Length: 2048
Subkey-Type: RSA
Subkey-Length: 2048
Name-Real: PROTON Bridge
Name-Email: $PROTON_USERNAME
Expire-Date: 2y
Passphrase: $PROTON_PASSWORD
%commit
%echo GPG key generation complete
EOF
    
    # Generiere GPG Key
    gpg --batch --generate-key "/tmp/gpg-batch"
    rm -f "/tmp/gpg-batch"
    
    # Initialisiere Pass
    pass init "$PROTON_USERNAME"
    
    # Test Pass
    echo "test-password" | pass insert -e "test/password"
    pass show "test/password"
    pass rm "test/password"
    
    log "✅ GPG und Pass eingerichtet"
}

# Systemd Services für socat erstellen
create_socat_services() {
    log "Erstelle socat Systemd Services..."
    
    # SMTP Service
    cat > "/etc/systemd/system/proton-socat-smtp.service" << EOF
[Unit]
Description=Socat Service for Proton Bridge SMTP Port Forwarding
After=network.target
Wants=protonmail-bridge.service

[Service]
Type=simple
ExecStart=/usr/bin/socat TCP4-LISTEN:587,fork,bind=$CONTAINER_IP TCP4:127.0.0.1:1025
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
EOF
    
    # IMAP Service
    cat > "/etc/systemd/system/proton-socat-imap.service" << EOF
[Unit]
Description=Socat Service for Proton Bridge IMAP Port Forwarding
After=network.target
Wants=protonmail-bridge.service

[Service]
Type=simple
ExecStart=/usr/bin/socat TCP4-LISTEN:143,fork,bind=$CONTAINER_IP TCP4:127.0.0.1:1143
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
EOF
    
    log "✅ Socat Services erstellt"
}

# PROTON Bridge Systemd Service erstellen
create_bridge_service() {
    log "Erstelle PROTON Bridge Systemd Service..."
    
    cat > "/etc/systemd/system/protonmail-bridge.service" << EOF
[Unit]
Description=PROTON Mail Bridge for nk-it.cloud
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=$PROJECT_DIR
Environment=HOME=/root
Environment=DISPLAY=:99
EnvironmentFile=$PROJECT_DIR/.env
ExecStartPre=/bin/bash -c 'export DISPLAY=:99; Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &'
ExecStart=/usr/bin/protonmail-bridge --noninteractive --log-level=info
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=append:$PROJECT_DIR/logs/bridge.log
StandardError=append:$PROJECT_DIR/logs/bridge-error.log

[Install]
WantedBy=multi-user.target
EOF
    
    log "✅ PROTON Bridge Service erstellt"
}

# Bridge Authentifizierung Setup
setup_bridge_authentication() {
    log "Richte Bridge-Authentifizierung ein..."
    
    # Erstelle Authentifizierungs-Skript
    cat > "$PROJECT_DIR/scripts/bridge-auth-native.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Native Authentifizierung
# ======================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

source "$PROJECT_DIR/.env"

echo "🔐 Starte PROTON Bridge Authentifizierung..."

# Starte Bridge im Screen für interaktive Authentifizierung
screen -dmS protonmail-auth bash -c "
    protonmail-bridge --cli --no-window << BRIDGE_EOF
login
$PROTON_USERNAME
$PROTON_PASSWORD
info
list
BRIDGE_EOF
"

echo "✅ Authentifizierung gestartet in Screen-Session 'protonmail-auth'"
echo "Verbinde mit: screen -r protonmail-auth"
echo "Beende mit: CTRL+A+D"
EOF
    
    chmod +x "$PROJECT_DIR/scripts/bridge-auth-native.sh"
    
    log "✅ Bridge-Authentifizierung Setup erstellt"
}

# Firewall konfigurieren
configure_firewall() {
    log "Konfiguriere Firewall..."
    
    # UFW zurücksetzen und konfigurieren
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    
    # SSH erlauben
    ufw allow ssh
    
    # PROTON Bridge Ports für lokales Netzwerk
    ufw allow from *********/24 to any port 143 comment "PROTON Bridge IMAP"
    ufw allow from *********/24 to any port 587 comment "PROTON Bridge SMTP"
    ufw allow from *********/24 to any port 25 comment "PROTON Bridge SMTP Alt"
    
    # Lokale Verbindungen
    ufw allow from 127.0.0.1
    
    # Aktiviere Firewall
    ufw --force enable
    
    log "✅ Firewall konfiguriert"
}

# Services aktivieren
enable_services() {
    log "Aktiviere Services..."
    
    # Systemd Daemon neu laden
    systemctl daemon-reload
    
    # Services aktivieren und starten
    systemctl enable protonmail-bridge
    systemctl enable proton-socat-smtp
    systemctl enable proton-socat-imap
    
    log "✅ Services aktiviert"
}

# Status prüfen
check_status() {
    log "Prüfe Service-Status..."
    
    # Service Status
    systemctl status protonmail-bridge --no-pager || true
    systemctl status proton-socat-smtp --no-pager || true
    systemctl status proton-socat-imap --no-pager || true
    
    # Port Tests
    sleep 5
    nc -z localhost 1143 && log "✅ IMAP Port 1143 OK" || log "❌ IMAP Port 1143 nicht erreichbar"
    nc -z localhost 1025 && log "✅ SMTP Port 1025 OK" || log "❌ SMTP Port 1025 nicht erreichbar"
    nc -z "$CONTAINER_IP" 143 && log "✅ External IMAP Port 143 OK" || log "❌ External IMAP Port 143 nicht erreichbar"
    nc -z "$CONTAINER_IP" 587 && log "✅ External SMTP Port 587 OK" || log "❌ External SMTP Port 587 nicht erreichbar"
}

# Erstelle Management-Skripte
create_management_scripts() {
    log "Erstelle Management-Skripte..."
    
    # Start-Skript
    cat > "$PROJECT_DIR/scripts/start-native-bridge.sh" << 'EOF'
#!/bin/bash
echo "🚀 Starte PROTON Bridge Native Services..."
sudo systemctl start protonmail-bridge
sudo systemctl start proton-socat-smtp
sudo systemctl start proton-socat-imap
echo "✅ Services gestartet"
EOF
    
    # Stop-Skript
    cat > "$PROJECT_DIR/scripts/stop-native-bridge.sh" << 'EOF'
#!/bin/bash
echo "🛑 Stoppe PROTON Bridge Native Services..."
sudo systemctl stop proton-socat-smtp
sudo systemctl stop proton-socat-imap
sudo systemctl stop protonmail-bridge
echo "✅ Services gestoppt"
EOF
    
    # Status-Skript
    cat > "$PROJECT_DIR/scripts/status-native-bridge.sh" << 'EOF'
#!/bin/bash
echo "📊 PROTON Bridge Native Status"
echo "=============================="
sudo systemctl status protonmail-bridge --no-pager
echo ""
sudo systemctl status proton-socat-smtp --no-pager
echo ""
sudo systemctl status proton-socat-imap --no-pager
echo ""
echo "Port Tests:"
nc -z localhost 1143 && echo "✅ IMAP localhost:1143 OK" || echo "❌ IMAP localhost:1143 nicht erreichbar"
nc -z localhost 1025 && echo "✅ SMTP localhost:1025 OK" || echo "❌ SMTP localhost:1025 nicht erreichbar"
EOF
    
    chmod +x "$PROJECT_DIR/scripts"/*native-bridge.sh
    
    log "✅ Management-Skripte erstellt"
}

# Hauptfunktion
main() {
    log "Starte PROTON Bridge Native Installation..."
    
    install_dependencies
    download_and_install_bridge
    setup_gpg_and_pass
    create_socat_services
    create_bridge_service
    setup_bridge_authentication
    configure_firewall
    enable_services
    create_management_scripts
    check_status
    
    log "🎉 Native Installation abgeschlossen!"
    log ""
    log "Nächste Schritte:"
    log "1. Authentifizierung: ./scripts/bridge-auth-native.sh"
    log "2. Services starten: ./scripts/start-native-bridge.sh"
    log "3. Status prüfen: ./scripts/status-native-bridge.sh"
    log ""
    log "Mail-Client Konfiguration:"
    log "IMAP Server: $CONTAINER_IP:143"
    log "SMTP Server: $CONTAINER_IP:587"
    log ""
    log "Logs:"
    log "Bridge: $PROJECT_DIR/logs/bridge.log"
    log "System: journalctl -u protonmail-bridge -f"
}

# Script ausführen
main "$@"
