#!/bin/bash

# PROTON Mail Bridge Authentifizierung Setup
# ==========================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Lade Umgebungsvariablen
if [[ -f "$PROJECT_DIR/.env" ]]; then
    source "$PROJECT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🔐 PROTON Mail Bridge Authentifizierung"
echo "======================================"

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/auth.log"
}

# Prüfe ob Bridge läuft
check_bridge_running() {
    if ! pgrep -f "protonmail-bridge" > /dev/null; then
        log "❌ PROTON Bridge läuft nicht. Starte zuerst den Bridge-Service."
        exit 1
    fi
    log "✅ PROTON Bridge läuft"
}

# Interaktive Authentifizierung
interactive_auth() {
    log "Starte interaktive Authentifizierung..."
    
    echo "Bitte gib deine PROTON Mail Zugangsdaten ein:"
    
    # Username eingeben
    if [[ -z "$PROTON_USERNAME" ]]; then
        read -p "PROTON Username (z.B. <EMAIL>): " PROTON_USERNAME
        echo "PROTON_USERNAME=$PROTON_USERNAME" >> "$PROJECT_DIR/.env"
    fi
    
    # Password eingeben
    if [[ -z "$PROTON_PASSWORD" ]]; then
        read -s -p "PROTON Password: " PROTON_PASSWORD
        echo
        echo "PROTON_PASSWORD=$PROTON_PASSWORD" >> "$PROJECT_DIR/.env"
    fi
    
    log "Zugangsdaten gespeichert"
}

# Bridge CLI Authentifizierung
bridge_auth() {
    log "Führe Bridge-Authentifizierung durch..."
    
    # Starte Bridge im CLI Modus für Authentifizierung
    protonmail-bridge --cli << EOF
login
$PROTON_USERNAME
$PROTON_PASSWORD
info
list
EOF
    
    if [[ $? -eq 0 ]]; then
        log "✅ Authentifizierung erfolgreich"
    else
        log "❌ Authentifizierung fehlgeschlagen"
        exit 1
    fi
}

# Bridge Credentials extrahieren
extract_credentials() {
    log "Extrahiere Bridge-Zugangsdaten..."
    
    # Warte bis Bridge bereit ist
    sleep 5
    
    # Hole Bridge-Zugangsdaten
    BRIDGE_INFO=$(protonmail-bridge --cli -c "info")
    
    if [[ -n "$BRIDGE_INFO" ]]; then
        # Extrahiere Username und Password aus Bridge Info
        BRIDGE_USERNAME=$(echo "$BRIDGE_INFO" | grep -oP 'Username: \K.*')
        BRIDGE_PASSWORD=$(echo "$BRIDGE_INFO" | grep -oP 'Password: \K.*')
        
        # Aktualisiere .env Datei
        sed -i "s/^BRIDGE_USERNAME=.*/BRIDGE_USERNAME=$BRIDGE_USERNAME/" "$PROJECT_DIR/.env"
        sed -i "s/^BRIDGE_PASSWORD=.*/BRIDGE_PASSWORD=$BRIDGE_PASSWORD/" "$PROJECT_DIR/.env"
        
        log "✅ Bridge-Zugangsdaten extrahiert und gespeichert"
    else
        log "⚠️  Warnung: Konnte Bridge-Zugangsdaten nicht automatisch extrahieren"
        log "Bitte prüfe die Bridge-Konfiguration manuell"
    fi
}

# Konfiguration testen
test_configuration() {
    log "Teste Konfiguration..."
    
    # Teste IMAP Verbindung
    if nc -z localhost $BRIDGE_IMAP_PORT; then
        log "✅ IMAP Port $BRIDGE_IMAP_PORT erreichbar"
    else
        log "❌ IMAP Port $BRIDGE_IMAP_PORT nicht erreichbar"
    fi
    
    # Teste SMTP Verbindung
    if nc -z localhost $BRIDGE_SMTP_PORT; then
        log "✅ SMTP Port $BRIDGE_SMTP_PORT erreichbar"
    else
        log "❌ SMTP Port $BRIDGE_SMTP_PORT nicht erreichbar"
    fi
}

# Backup der Konfiguration
backup_config() {
    log "Erstelle Backup der Konfiguration..."
    
    BACKUP_DIR="$PROJECT_DIR/data/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Kopiere wichtige Konfigurationsdateien
    cp -r ~/.cache/protonmail/bridge/* "$BACKUP_DIR/" 2>/dev/null || true
    cp -r ~/.config/protonmail-bridge/* "$BACKUP_DIR/" 2>/dev/null || true
    cp "$PROJECT_DIR/.env" "$BACKUP_DIR/"
    
    log "✅ Backup erstellt in: $BACKUP_DIR"
}

# Hauptfunktion
main() {
    log "Starte Authentifizierung Setup..."
    
    check_bridge_running
    interactive_auth
    bridge_auth
    extract_credentials
    test_configuration
    backup_config
    
    log "🎉 Authentifizierung Setup abgeschlossen!"
    log ""
    log "Bridge-Zugangsdaten:"
    log "IMAP Server: localhost:$BRIDGE_IMAP_PORT"
    log "SMTP Server: localhost:$BRIDGE_SMTP_PORT"
    log "Username: $BRIDGE_USERNAME"
    log "Password: [siehe .env Datei]"
    log ""
    log "Nächste Schritte:"
    log "1. Starte den Bridge-Service: ./scripts/start-bridge.sh"
    log "2. Teste die Verbindung: ./tests/test-connection.sh"
}

# Script ausführen
main "$@"
