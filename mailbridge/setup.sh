#!/bin/bash

# PROTON Mail Bridge Komplettes Setup für nk-it.cloud
# ===================================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

echo "🚀 PROTON Mail Bridge Setup für nk-it.cloud"
echo "============================================"
echo "Container IP: ***********"
echo "Domain: nk-it.cloud"
echo "Proxmox LXC Container Setup"
echo ""

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/setup.log"
}

# Prüfe Root-Rechte für bestimmte Schritte
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log "⚠️  Läuft als root - einige Schritte werden übersprungen"
        IS_ROOT=true
    else
        log "Läuft als normaler Benutzer - sudo wird für privilegierte Operationen benötigt"
        IS_ROOT=false
    fi
}

# Zeige Setup-Übersicht
show_overview() {
    log "📋 Setup-Übersicht:"
    log "1. ✅ Verzeichnisstruktur erstellt"
    log "2. ✅ Konfigurationsdateien erstellt"
    log "3. ✅ Skripte erstellt und ausführbar gemacht"
    log "4. ✅ Test-Skripte erstellt"
    log "5. ✅ Dokumentation erstellt"
    log ""
    log "📁 Verzeichnisstruktur:"
    tree "$PROJECT_DIR" -I '__pycache__|*.pyc' || ls -la "$PROJECT_DIR"
}

# Zeige nächste Schritte
show_next_steps() {
    log ""
    log "🎯 Nächste Schritte für die Einrichtung:"
    log "========================================"
    log ""
    log "1. 📥 PROTON Bridge installieren:"
    log "   sudo ./scripts/install-bridge.sh"
    log ""
    log "2. 🔐 Authentifizierung einrichten:"
    log "   ./config/auth-setup.sh"
    log ""
    log "3. 🌐 Netzwerk konfigurieren:"
    log "   sudo ./config/network-setup.sh"
    log ""
    log "4. 🚀 Service starten:"
    log "   ./scripts/start-bridge.sh"
    log ""
    log "5. 🧪 Tests durchführen:"
    log "   ./tests/test-connection.sh"
    log "   python3 ./tests/test-mail-client.py"
    log ""
    log "📧 Mail-Client Konfiguration:"
    log "   IMAP Server: ***********:1143"
    log "   SMTP Server: ***********:1025"
    log "   Verschlüsselung: Keine"
    log "   Zugangsdaten: Siehe .env Datei nach Authentifizierung"
    log ""
    log "📚 Dokumentation:"
    log "   README.md  - Vollständige Anleitung"
    log "   TODO.md    - Detaillierte Aufgabenliste"
    log ""
    log "🔧 Nützliche Befehle:"
    log "   ./scripts/monitor-network.sh  - Netzwerk-Status"
    log "   tail -f logs/bridge.log       - Live-Logs"
    log "   systemctl status proton-bridge - Service-Status"
}

# Erstelle Quick-Start Skript
create_quickstart() {
    log "Erstelle Quick-Start Skript..."
    
    cat > "$PROJECT_DIR/quickstart.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Quick-Start für nk-it.cloud
# =========================================

echo "🚀 PROTON Bridge Quick-Start"
echo "============================"

# Prüfe ob bereits installiert
if command -v protonmail-bridge &> /dev/null; then
    echo "✅ PROTON Bridge bereits installiert"
else
    echo "📥 Installiere PROTON Bridge..."
    sudo ./scripts/install-bridge.sh
fi

# Prüfe .env Konfiguration
if [[ -f ".env" ]] && grep -q "PROTON_USERNAME=your-username" .env; then
    echo "🔐 Richte Authentifizierung ein..."
    ./config/auth-setup.sh
fi

# Konfiguriere Netzwerk
echo "🌐 Konfiguriere Netzwerk..."
sudo ./config/network-setup.sh

# Starte Service
echo "🚀 Starte Bridge Service..."
./scripts/start-bridge.sh

# Führe Tests durch
echo "🧪 Führe Tests durch..."
./tests/test-connection.sh

echo ""
echo "🎉 Quick-Start abgeschlossen!"
echo "Prüfe die Logs für Details: tail -f logs/*.log"
EOF
    
    chmod +x "$PROJECT_DIR/quickstart.sh"
    log "✅ Quick-Start Skript erstellt: ./quickstart.sh"
}

# Erstelle Status-Check Skript
create_status_check() {
    log "Erstelle Status-Check Skript..."
    
    cat > "$PROJECT_DIR/status.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Status Check
# ==========================

echo "📊 PROTON Bridge Status für nk-it.cloud"
echo "========================================"

# Service Status
echo "🔧 Service Status:"
if systemctl is-active --quiet proton-bridge 2>/dev/null; then
    echo "✅ Service läuft"
else
    echo "❌ Service läuft nicht"
fi

# Prozess Status
echo ""
echo "⚙️  Prozess Status:"
if pgrep -f "protonmail-bridge" > /dev/null; then
    echo "✅ Bridge Prozess aktiv"
    ps aux | grep protonmail-bridge | grep -v grep
else
    echo "❌ Bridge Prozess nicht gefunden"
fi

# Port Status
echo ""
echo "🌐 Port Status:"
nc -z localhost 1143 && echo "✅ IMAP Port 1143 OK" || echo "❌ IMAP Port 1143 nicht erreichbar"
nc -z localhost 1025 && echo "✅ SMTP Port 1025 OK" || echo "❌ SMTP Port 1025 nicht erreichbar"
nc -z localhost 8080 && echo "✅ API Port 8080 OK" || echo "❌ API Port 8080 nicht erreichbar"

# Netzwerk Status
echo ""
echo "📡 Netzwerk Status:"
ip addr show | grep "***********" && echo "✅ Container IP konfiguriert" || echo "❌ Container IP nicht gefunden"

# Firewall Status
echo ""
echo "🔒 Firewall Status:"
if command -v ufw &> /dev/null; then
    ufw status | head -5
else
    echo "UFW nicht installiert"
fi

# Log Status
echo ""
echo "📝 Aktuelle Logs (letzte 5 Zeilen):"
if [[ -f "logs/bridge.log" ]]; then
    tail -5 logs/bridge.log
else
    echo "Keine Bridge-Logs gefunden"
fi
EOF
    
    chmod +x "$PROJECT_DIR/status.sh"
    log "✅ Status-Check Skript erstellt: ./status.sh"
}

# Erstelle Backup-Skript
create_backup_script() {
    log "Erstelle Backup-Skript..."
    
    cat > "$PROJECT_DIR/backup.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Backup
# ====================

BACKUP_DIR="./data/backup/manual_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "💾 Erstelle Backup in: $BACKUP_DIR"

# Backup wichtiger Dateien
cp .env "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  .env nicht gefunden"
cp -r config/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  config/ nicht gefunden"
cp -r data/keychain/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  keychain/ nicht gefunden"
cp -r logs/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  logs/ nicht gefunden"

# Erstelle Backup-Info
cat > "$BACKUP_DIR/backup-info.txt" << BACKUP_EOF
PROTON Bridge Backup
===================
Erstellt: $(date)
Hostname: $(hostname)
Container IP: ***********
Domain: nk-it.cloud

Enthaltene Dateien:
- .env (Konfiguration)
- config/ (Konfigurationsdateien)
- data/keychain/ (Schlüssel)
- logs/ (Log-Dateien)
BACKUP_EOF

echo "✅ Backup erstellt: $BACKUP_DIR"
echo "📁 Backup-Größe: $(du -sh "$BACKUP_DIR" | cut -f1)"
EOF
    
    chmod +x "$PROJECT_DIR/backup.sh"
    log "✅ Backup-Skript erstellt: ./backup.sh"
}

# Validiere Setup
validate_setup() {
    log "🔍 Validiere Setup..."
    
    local errors=0
    
    # Prüfe wichtige Dateien
    local required_files=(
        ".env"
        "docker-compose.yml"
        "README.md"
        "TODO.md"
        "scripts/install-bridge.sh"
        "scripts/start-bridge.sh"
        "config/auth-setup.sh"
        "config/network-setup.sh"
        "tests/test-connection.sh"
        "tests/test-mail-client.py"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$PROJECT_DIR/$file" ]]; then
            log "✅ $file vorhanden"
        else
            log "❌ $file fehlt"
            errors=$((errors + 1))
        fi
    done
    
    # Prüfe Verzeichnisse
    local required_dirs=(
        "config"
        "scripts"
        "tests"
        "data"
        "logs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$PROJECT_DIR/$dir" ]]; then
            log "✅ $dir/ vorhanden"
        else
            log "❌ $dir/ fehlt"
            errors=$((errors + 1))
        fi
    done
    
    # Prüfe Berechtigungen
    if [[ -x "$PROJECT_DIR/scripts/install-bridge.sh" ]]; then
        log "✅ Skripte sind ausführbar"
    else
        log "❌ Skripte nicht ausführbar"
        errors=$((errors + 1))
    fi
    
    if [[ $errors -eq 0 ]]; then
        log "🎉 Setup-Validierung erfolgreich!"
        return 0
    else
        log "❌ Setup-Validierung fehlgeschlagen ($errors Fehler)"
        return 1
    fi
}

# Hauptfunktion
main() {
    log "Starte PROTON Mail Bridge Setup-Validierung..."
    
    check_permissions
    show_overview
    create_quickstart
    create_status_check
    create_backup_script
    validate_setup
    show_next_steps
    
    log ""
    log "🎉 PROTON Mail Bridge Setup für nk-it.cloud ist bereit!"
    log "Alle notwendigen Dateien und Skripte wurden erstellt."
    log "Folge den Anweisungen in der TODO.md oder führe ./quickstart.sh aus."
}

# Script ausführen
main "$@"
