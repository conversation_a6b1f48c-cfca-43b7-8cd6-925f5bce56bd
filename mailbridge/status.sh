#!/bin/bash

# PROTON Bridge Status Check
# ==========================

echo "📊 PROTON Bridge Status für nk-it.cloud"
echo "========================================"

# Service Status
echo "🔧 Service Status:"
if systemctl is-active --quiet proton-bridge 2>/dev/null; then
    echo "✅ Service läuft"
else
    echo "❌ Service läuft nicht"
fi

# Prozess Status
echo ""
echo "⚙️  Prozess Status:"
if pgrep -f "protonmail-bridge" > /dev/null; then
    echo "✅ Bridge Prozess aktiv"
    ps aux | grep protonmail-bridge | grep -v grep
else
    echo "❌ Bridge Prozess nicht gefunden"
fi

# Port Status
echo ""
echo "🌐 Port Status:"
nc -z localhost 1143 && echo "✅ IMAP Port 1143 OK" || echo "❌ IMAP Port 1143 nicht erreichbar"
nc -z localhost 1025 && echo "✅ SMTP Port 1025 OK" || echo "❌ SMTP Port 1025 nicht erreichbar"
nc -z localhost 8080 && echo "✅ API Port 8080 OK" || echo "❌ API Port 8080 nicht erreichbar"

# Netzwerk Status
echo ""
echo "📡 Netzwerk Status:"
ip addr show | grep "***********" && echo "✅ Container IP konfiguriert" || echo "❌ Container IP nicht gefunden"

# Firewall Status
echo ""
echo "🔒 Firewall Status:"
if command -v ufw &> /dev/null; then
    ufw status | head -5
else
    echo "UFW nicht installiert"
fi

# Log Status
echo ""
echo "📝 Aktuelle Logs (letzte 5 Zeilen):"
if [[ -f "logs/bridge.log" ]]; then
    tail -5 logs/bridge.log
else
    echo "Keine Bridge-Logs gefunden"
fi
