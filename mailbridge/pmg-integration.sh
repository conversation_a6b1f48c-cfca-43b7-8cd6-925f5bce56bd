#!/bin/bash

# PMG → PROTON Bridge Integration
# ===============================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Lade Umgebungsvariablen
if [[ -f "$SCRIPT_DIR/.env" ]]; then
    source "$SCRIPT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🔗 PMG → PROTON Bridge Integration"
echo "=================================="
echo "PMG: $PMG_IP"
echo "Bridge: $CONTAINER_IP"
echo "Domain: $PMG_DOMAIN"
echo ""

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$SCRIPT_DIR/logs/pmg-integration.log"
}

# PMG API Call Funktion
pmg_api_call() {
    local method="$1"
    local endpoint="$2"
    local data="$3"

    # Versuche zuerst HTTPS, dann HTTP
    local url_https="https://$PMG_IP:8006/api2/json$endpoint"
    local url_http="http://$PMG_IP:8006/api2/json$endpoint"

    if [[ -n "$data" ]]; then
        # Versuche HTTPS
        local result=$(curl -k -s -X "$method" \
             -H "Content-Type: application/x-www-form-urlencoded" \
             -u "$PMG_USERNAME:$PMG_PASSWORD" \
             -d "$data" \
             "$url_https" 2>/dev/null)

        # Falls HTTPS fehlschlägt, versuche HTTP
        if [[ -z "$result" || "$result" == *"error"* ]]; then
            curl -s -X "$method" \
                 -H "Content-Type: application/x-www-form-urlencoded" \
                 -u "$PMG_USERNAME:$PMG_PASSWORD" \
                 -d "$data" \
                 "$url_http"
        else
            echo "$result"
        fi
    else
        # Versuche HTTPS
        local result=$(curl -k -s -X "$method" \
             -u "$PMG_USERNAME:$PMG_PASSWORD" \
             "$url_https" 2>/dev/null)

        # Falls HTTPS fehlschlägt, versuche HTTP
        if [[ -z "$result" || "$result" == *"error"* ]]; then
            curl -s -X "$method" \
                 -u "$PMG_USERNAME:$PMG_PASSWORD" \
                 "$url_http"
        else
            echo "$result"
        fi
    fi
}

# PMG Verbindung testen
test_pmg_connection() {
    log "Teste PMG Verbindung..."
    
    local response=$(pmg_api_call "GET" "/version")
    
    if echo "$response" | grep -q "version"; then
        local version=$(echo "$response" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
        log "✅ PMG Verbindung OK (Version: $version)"
        return 0
    else
        log "❌ PMG Verbindung fehlgeschlagen"
        return 1
    fi
}

# Bridge Verbindung testen
test_bridge_connection() {
    log "Teste Bridge Verbindung..."
    
    if nc -z "$CONTAINER_IP" "$BRIDGE_SMTP_PORT"; then
        log "✅ Bridge SMTP ($CONTAINER_IP:$BRIDGE_SMTP_PORT) erreichbar"
    else
        log "❌ Bridge SMTP nicht erreichbar"
        return 1
    fi
    
    if nc -z "$CONTAINER_IP" "$BRIDGE_IMAP_PORT"; then
        log "✅ Bridge IMAP ($CONTAINER_IP:$BRIDGE_IMAP_PORT) erreichbar"
    else
        log "❌ Bridge IMAP nicht erreichbar"
        return 1
    fi
    
    return 0
}

# PMG Transport konfigurieren
configure_pmg_transport() {
    log "Konfiguriere PMG Transport für $PMG_DOMAIN..."
    
    # Transport für nk-it.cloud Domain
    local transport_data="domain=$PMG_DOMAIN&host=$PMG_TRANSPORT_HOST&port=$PMG_TRANSPORT_PORT&protocol=smtp"
    
    local response=$(pmg_api_call "POST" "/config/transport" "$transport_data")
    
    if echo "$response" | grep -q "success"; then
        log "✅ PMG Transport konfiguriert: $PMG_DOMAIN → $PMG_TRANSPORT_HOST:$PMG_TRANSPORT_PORT"
    else
        log "⚠️  Transport möglicherweise bereits konfiguriert"
    fi
}

# PMG Relay Domain konfigurieren
configure_pmg_relay() {
    log "Konfiguriere PMG Relay Domain..."
    
    local relay_data="domain=$PMG_DOMAIN&comment=PROTON Bridge Relay"
    
    local response=$(pmg_api_call "POST" "/config/domains" "$relay_data")
    
    if echo "$response" | grep -q "success"; then
        log "✅ PMG Relay Domain konfiguriert: $PMG_DOMAIN"
    else
        log "⚠️  Relay Domain möglicherweise bereits konfiguriert"
    fi
}

# PMG Netzwerk konfigurieren
configure_pmg_networks() {
    log "Konfiguriere PMG Netzwerke..."
    
    # Lokales Netzwerk hinzufügen
    local network_data="cidr=*********/24&comment=Local Network"
    
    local response=$(pmg_api_call "POST" "/config/mynetworks" "$network_data")
    
    if echo "$response" | grep -q "success"; then
        log "✅ PMG Netzwerk konfiguriert: *********/24"
    else
        log "⚠️  Netzwerk möglicherweise bereits konfiguriert"
    fi
}

# PMG Status anzeigen
show_pmg_status() {
    log "PMG Status abfragen..."
    
    echo ""
    echo "📊 PMG Konfiguration:"
    echo "===================="
    
    # Transport Status
    local transports=$(pmg_api_call "GET" "/config/transport")
    echo "🚚 Transports:"
    echo "$transports" | grep -o '"domain":"[^"]*"' | cut -d'"' -f4 || echo "Keine Transports konfiguriert"
    
    # Relay Domains
    local domains=$(pmg_api_call "GET" "/config/domains")
    echo ""
    echo "🌐 Relay Domains:"
    echo "$domains" | grep -o '"domain":"[^"]*"' | cut -d'"' -f4 || echo "Keine Relay Domains konfiguriert"
    
    # Netzwerke
    local networks=$(pmg_api_call "GET" "/config/mynetworks")
    echo ""
    echo "🔗 Netzwerke:"
    echo "$networks" | grep -o '"cidr":"[^"]*"' | cut -d'"' -f4 || echo "Keine Netzwerke konfiguriert"
}

# Mail-Flow testen
test_mail_flow() {
    log "Teste Mail-Flow: Client → PMG → Bridge..."
    
    echo ""
    echo "📧 Mail-Flow Test:"
    echo "=================="
    
    # Test SMTP von PMG zu Bridge
    echo "1. PMG → Bridge SMTP Test:"
    if nc -z "$PMG_TRANSPORT_HOST" "$PMG_TRANSPORT_PORT"; then
        echo "✅ PMG kann Bridge SMTP erreichen"
    else
        echo "❌ PMG kann Bridge SMTP nicht erreichen"
    fi
    
    # Test Client zu PMG
    echo ""
    echo "2. Client → PMG Test:"
    if nc -z "$PMG_IP" "$PMG_INTERNAL_PORT"; then
        echo "✅ Client kann PMG SMTP erreichen"
    else
        echo "❌ Client kann PMG SMTP nicht erreichen"
    fi
    
    echo ""
    echo "📋 Mail-Client Konfiguration:"
    echo "IMAP Server: $PMG_IP:143"
    echo "SMTP Server: $PMG_IP:$PMG_INTERNAL_PORT"
    echo "Domain: $PMG_DOMAIN"
}

# Monitoring Setup
setup_monitoring() {
    log "Richte Monitoring ein..."
    
    # Erstelle Monitoring Skript
    cat > "$SCRIPT_DIR/monitor-pmg-bridge.sh" << 'EOF'
#!/bin/bash

# PMG → Bridge Monitoring
# ======================

source "$(dirname "$0")/.env"

echo "📊 PMG → Bridge Status"
echo "====================="

# PMG Status
echo "🔧 PMG Status:"
if nc -z "$PMG_IP" 8006; then
    echo "✅ PMG Web-Interface erreichbar"
else
    echo "❌ PMG Web-Interface nicht erreichbar"
fi

# Bridge Status
echo ""
echo "🌉 Bridge Status:"
if nc -z "$CONTAINER_IP" "$BRIDGE_SMTP_PORT"; then
    echo "✅ Bridge SMTP erreichbar"
else
    echo "❌ Bridge SMTP nicht erreichbar"
fi

if nc -z "$CONTAINER_IP" "$BRIDGE_IMAP_PORT"; then
    echo "✅ Bridge IMAP erreichbar"
else
    echo "❌ Bridge IMAP nicht erreichbar"
fi

# Mail-Flow Test
echo ""
echo "📧 Mail-Flow:"
echo "Client → PMG ($PMG_IP:$PMG_INTERNAL_PORT)"
echo "PMG → Bridge ($PMG_TRANSPORT_HOST:$PMG_TRANSPORT_PORT)"
echo "Bridge → PROTON Mail"

# Docker Status
echo ""
echo "🐳 Docker Status:"
docker compose ps
EOF
    
    chmod +x "$SCRIPT_DIR/monitor-pmg-bridge.sh"
    log "✅ Monitoring Skript erstellt: monitor-pmg-bridge.sh"
}

# Hauptfunktion
main() {
    log "Starte PMG → PROTON Bridge Integration..."
    
    test_pmg_connection
    test_bridge_connection
    configure_pmg_transport
    configure_pmg_relay
    configure_pmg_networks
    show_pmg_status
    test_mail_flow
    setup_monitoring
    
    log "🎉 PMG Integration abgeschlossen!"
    echo ""
    echo "📋 Nächste Schritte:"
    echo "1. Mail-Clients auf PMG umstellen:"
    echo "   IMAP: $PMG_IP:143"
    echo "   SMTP: $PMG_IP:$PMG_INTERNAL_PORT"
    echo ""
    echo "2. Monitoring: ./monitor-pmg-bridge.sh"
    echo "3. PMG Web-Interface: https://$PMG_IP:8006"
    echo ""
    echo "🔗 Mail-Flow: Client → PMG → Bridge → PROTON"
}

# Script ausführen
main "$@"
