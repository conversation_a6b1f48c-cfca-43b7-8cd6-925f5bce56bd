#!/bin/bash

# PROTON Mail Bridge - Implementierung Auswahl
# ============================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

echo "🎯 PROTON Mail Bridge - Implementierung wählen"
echo "=============================================="
echo ""

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/implementation.log"
}

# System-Analyse
analyze_system() {
    log "Analysiere System..."
    
    # Architektur prüfen
    ARCH=$(uname -m)
    log "Architektur: $ARCH"
    
    # LXC Container prüfen
    if [[ -f "/proc/1/environ" ]] && grep -q "container=lxc" /proc/1/environ; then
        IS_LXC=true
        log "✅ LXC Container erkannt"
    else
        IS_LXC=false
        log "⚠️  Kein LXC Container"
    fi
    
    # Docker verfügbar prüfen
    if command -v docker &> /dev/null && systemctl is-active --quiet docker; then
        DOCKER_AVAILABLE=true
        log "✅ Docker verfügbar"
    else
        DOCKER_AVAILABLE=false
        log "❌ Docker nicht verfügbar"
    fi
    
    # Docker Compose prüfen
    if command -v "docker compose" &> /dev/null || command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_AVAILABLE=true
        log "✅ Docker Compose verfügbar"
    else
        DOCKER_COMPOSE_AVAILABLE=false
        log "❌ Docker Compose nicht verfügbar"
    fi
    
    # Speicher prüfen
    MEMORY_MB=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    log "Verfügbarer Speicher: ${MEMORY_MB}MB"
    
    # CPU Kerne prüfen
    CPU_CORES=$(nproc)
    log "CPU Kerne: $CPU_CORES"
}

# Empfehlung basierend auf System-Analyse
get_recommendation() {
    log "Erstelle Empfehlung..."
    
    # Standard: Docker deb-basiert
    RECOMMENDED="docker-deb"
    REASON="Standard-Empfehlung für Stabilität"
    
    # LXC Container mit wenig Speicher -> Native
    if [[ "$IS_LXC" == true ]] && [[ $MEMORY_MB -lt 1024 ]]; then
        RECOMMENDED="native"
        REASON="LXC Container mit begrenztem Speicher - Native Installation optimal"
    fi
    
    # Kein Docker verfügbar -> Native
    if [[ "$DOCKER_AVAILABLE" == false ]]; then
        RECOMMENDED="native"
        REASON="Docker nicht verfügbar - Native Installation erforderlich"
    fi
    
    # ARM Architektur -> Build-basiert
    if [[ "$ARCH" =~ ^(arm|aarch64) ]]; then
        if [[ "$DOCKER_AVAILABLE" == true ]]; then
            RECOMMENDED="docker-build"
            REASON="ARM Architektur - Build-basierte Docker Version erforderlich"
        else
            RECOMMENDED="native"
            REASON="ARM Architektur ohne Docker - Native Installation"
        fi
    fi
    
    # Sehr wenig Speicher -> SMTP-only
    if [[ $MEMORY_MB -lt 512 ]]; then
        RECOMMENDED="smtp-only"
        REASON="Sehr wenig Speicher - Nur SMTP für E-Mail Versand"
    fi
    
    log "Empfehlung: $RECOMMENDED ($REASON)"
}

# Zeige verfügbare Optionen
show_options() {
    echo ""
    echo "📋 Verfügbare Implementierungen:"
    echo "================================"
    echo ""
    echo "1. 🐳 Docker (deb-basiert) - EMPFOHLEN"
    echo "   ✅ Stabil und getestet"
    echo "   ✅ Einfache Einrichtung"
    echo "   ❌ Nur amd64 Architektur"
    echo ""
    echo "2. 🐳 Docker (build-basiert)"
    echo "   ✅ Multi-Architektur Support"
    echo "   ✅ Neueste Features"
    echo "   ❌ Weniger getestet"
    echo ""
    echo "3. 🔧 Native Installation"
    echo "   ✅ Beste Performance in LXC"
    echo "   ✅ Geringster Overhead"
    echo "   ❌ Komplexere Einrichtung"
    echo ""
    echo "4. 📧 SMTP-Only (Docker)"
    echo "   ✅ Nur für E-Mail Versand"
    echo "   ✅ Geringer Speicherverbrauch"
    echo "   ❌ Kein E-Mail Empfang"
    echo ""
    echo "5. 🔒 Localhost-Only (Docker)"
    echo "   ✅ Maximale Sicherheit"
    echo "   ✅ Nur lokaler Zugriff"
    echo "   ❌ Zusätzliche Konfiguration für Netzwerk-Zugriff"
    echo ""
}

# Automatische Installation basierend auf Empfehlung
auto_install() {
    local choice="$1"
    
    case "$choice" in
        "docker-deb"|"1")
            log "Starte Docker (deb-basiert) Installation..."
            docker compose up -d proton-bridge
            ;;
        "docker-build"|"2")
            log "Starte Docker (build-basiert) Installation..."
            docker compose --profile build up -d
            ;;
        "native"|"3")
            log "Starte Native Installation..."
            sudo ./scripts/install-native-bridge.sh
            ;;
        "smtp-only"|"4")
            log "Starte SMTP-Only Installation..."
            docker compose --profile smtp-only up -d
            ;;
        "localhost"|"5")
            log "Starte Localhost-Only Installation..."
            docker compose --profile secure up -d
            ;;
        *)
            log "❌ Ungültige Auswahl: $choice"
            return 1
            ;;
    esac
}

# Interaktive Auswahl
interactive_selection() {
    echo "🎯 Empfehlung für Ihr System: $RECOMMENDED"
    echo "Grund: $REASON"
    echo ""
    
    read -p "Möchten Sie die Empfehlung verwenden? (Y/n): " use_recommendation
    
    if [[ "$use_recommendation" =~ ^[Nn]$ ]]; then
        echo ""
        echo "Bitte wählen Sie eine Option (1-5):"
        read -p "Ihre Wahl: " manual_choice
        
        case "$manual_choice" in
            1) SELECTED="docker-deb" ;;
            2) SELECTED="docker-build" ;;
            3) SELECTED="native" ;;
            4) SELECTED="smtp-only" ;;
            5) SELECTED="localhost" ;;
            *)
                echo "❌ Ungültige Auswahl"
                exit 1
                ;;
        esac
    else
        SELECTED="$RECOMMENDED"
    fi
    
    log "Gewählte Implementierung: $SELECTED"
}

# Voraussetzungen prüfen
check_prerequisites() {
    local implementation="$1"
    
    log "Prüfe Voraussetzungen für $implementation..."
    
    case "$implementation" in
        "docker-deb"|"docker-build"|"smtp-only"|"localhost")
            if [[ "$DOCKER_AVAILABLE" == false ]]; then
                log "❌ Docker erforderlich aber nicht verfügbar"
                echo "Installiere Docker..."
                curl -fsSL https://get.docker.com -o get-docker.sh
                sh get-docker.sh
                systemctl start docker
                systemctl enable docker
            fi
            
            if [[ "$DOCKER_COMPOSE_AVAILABLE" == false ]]; then
                log "❌ Docker Compose erforderlich aber nicht verfügbar"
                echo "Installiere Docker Compose Plugin..."
                apt-get update
                apt-get install -y docker-compose-plugin
            fi
            ;;
        "native")
            # Prüfe ob alle nativen Abhängigkeiten verfügbar sind
            local missing_deps=()
            for dep in gnupg pass socat systemctl; do
                if ! command -v "$dep" &> /dev/null; then
                    missing_deps+=("$dep")
                fi
            done
            
            if [[ ${#missing_deps[@]} -gt 0 ]]; then
                log "❌ Fehlende Abhängigkeiten: ${missing_deps[*]}"
                echo "Installiere fehlende Abhängigkeiten..."
                apt-get update
                apt-get install -y gnupg pass socat systemd
            fi
            ;;
    esac
    
    log "✅ Voraussetzungen erfüllt"
}

# Post-Installation Tests
run_post_install_tests() {
    local implementation="$1"
    
    log "Führe Post-Installation Tests durch..."
    
    # Warte auf Service-Start
    sleep 10
    
    case "$implementation" in
        "docker-deb"|"docker-build"|"smtp-only"|"localhost")
            # Docker Container Status
            if docker ps | grep -q "proton-bridge"; then
                log "✅ Docker Container läuft"
            else
                log "❌ Docker Container läuft nicht"
                return 1
            fi
            ;;
        "native")
            # Systemd Service Status
            if systemctl is-active --quiet protonmail-bridge; then
                log "✅ Native Service läuft"
            else
                log "❌ Native Service läuft nicht"
                return 1
            fi
            ;;
    esac
    
    # Port Tests
    ./tests/test-connection.sh
    
    log "✅ Post-Installation Tests abgeschlossen"
}

# Hauptfunktion
main() {
    log "Starte Implementierung-Auswahl..."
    
    analyze_system
    get_recommendation
    show_options
    
    # Prüfe ob automatische Installation gewünscht
    if [[ "$1" == "--auto" ]]; then
        SELECTED="$RECOMMENDED"
        log "Automatische Installation mit Empfehlung: $SELECTED"
    elif [[ -n "$1" ]]; then
        SELECTED="$1"
        log "Manuelle Auswahl: $SELECTED"
    else
        interactive_selection
    fi
    
    # Installation durchführen
    check_prerequisites "$SELECTED"
    auto_install "$SELECTED"
    run_post_install_tests "$SELECTED"
    
    log "🎉 Installation von $SELECTED abgeschlossen!"
    echo ""
    echo "📋 Nächste Schritte:"
    echo "1. Authentifizierung einrichten: ./config/auth-setup.sh"
    echo "2. Tests durchführen: ./tests/test-connection.sh"
    echo "3. Mail-Clients konfigurieren (siehe README.md)"
    echo ""
    echo "📊 Status prüfen: ./status.sh"
    echo "📝 Logs anzeigen: tail -f logs/*.log"
}

# Script ausführen
main "$@"
