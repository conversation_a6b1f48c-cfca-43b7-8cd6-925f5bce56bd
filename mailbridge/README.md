# PROTON Mail Bridge für nk-it.cloud

Komplette Einrichtung der PROTON Mail Bridge für IMAP/SMTP Zugriff auf nk-it.cloud E-Mail Adressen in einem LXC Container.

## 📋 Übersicht

Diese Installation ermöglicht es, PROTON Mail Konten über IMAP/SMTP in lokalen Mail-Clients zu verwenden. Die Bridge läuft in einem LXC Container mit der IP `***********` und stellt IMAP/SMTP Services für das lokale Netzwerk bereit.

## 🏗️ Architektur

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Mail Client   │───▶│  PROTON Bridge   │───▶│  PROTON Mail    │
│  (Thunderbird,  │    │  (LXC Container) │    │   (Internet)    │
│   Outlook, etc) │    │  ***********     │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   nk-it.cloud    │
                       │   Mail Domain    │
                       └──────────────────┘
```

## 🚀 Schnellstart

### Automatische Implementierung-Auswahl ⭐ **EMPFOHLEN**

```bash
# Automatische Analyse und Installation der besten Option
./choose-implementation.sh --auto
```

### Manuelle Implementierung-Auswahl

```bash
# Interaktive Auswahl der Implementierung
./choose-implementation.sh
```

### Klassische Installation

```bash
# 1. Alle Skripte ausführbar machen
chmod +x scripts/*.sh config/*.sh tests/*.sh

# 2. Wähle eine Implementierung:

# Option A: Docker (Standard) - EMPFOHLEN
docker compose up -d proton-bridge

# Option B: Native Installation (LXC optimiert)
sudo ./scripts/install-native-bridge.sh

# Option C: Erweiterte Docker Optionen
docker compose -f docker-compose.advanced.yml --profile [PROFIL] up -d
```

### 2. Konfiguration

```bash
# Bearbeite .env mit deinen PROTON Zugangsdaten
nano .env

# Authentifizierung einrichten
./config/auth-setup.sh
```

### 3. Service starten und testen

```bash
# Status prüfen
./status.sh

# Verbindung testen
./tests/test-connection.sh
```

## 📁 Verzeichnisstruktur

```
/opt/mailbridge/
├── .env                    # Umgebungsvariablen und Konfiguration
├── docker-compose.yml      # Docker Compose Konfiguration
├── README.md              # Diese Datei
├── config/                # Konfigurationsdateien
│   ├── bridge.conf        # Bridge Hauptkonfiguration
│   ├── auth-setup.sh      # Authentifizierung Setup
│   ├── network-setup.sh   # Netzwerk Konfiguration
│   ├── thunderbird-config.xml
│   ├── outlook-settings.txt
│   └── certs/             # SSL Zertifikate
├── scripts/               # Verwaltungsskripte
│   ├── install-bridge.sh  # Docker Installation
│   ├── install-native-bridge.sh # Native Installation
│   ├── start-bridge.sh    # Service starten
│   ├── setup-auth.sh      # Authentifizierung
│   ├── authenticate.sh    # Bridge Login
│   └── monitor-network.sh # Netzwerk Monitoring
├── tests/                 # Test-Skripte
│   ├── test-connection.sh # Verbindungstest
│   └── test-mail-client.py # Python Mail-Client Test
├── data/                  # Daten und Cache
│   ├── cache/             # Bridge Cache
│   ├── keychain/          # Sichere Schlüssel
│   └── backup/            # Backups
└── logs/                  # Log-Dateien
    ├── bridge.log         # Bridge Logs
    ├── install.log        # Installation Logs
    ├── auth.log           # Authentifizierung Logs
    └── test.log           # Test Logs
```

## ⚙️ Konfiguration

### Umgebungsvariablen (.env)

| Variable | Beschreibung | Standard |
|----------|--------------|----------|
| `PROTON_USERNAME` | PROTON E-Mail Adresse | - |
| `PROTON_PASSWORD` | PROTON Passwort | - |
| `CONTAINER_IP` | LXC Container IP | *********** |
| `BRIDGE_IMAP_PORT` | IMAP Port | 1143 |
| `BRIDGE_SMTP_PORT` | SMTP Port | 1025 |
| `BRIDGE_API_PORT` | API Port | 8080 |
| `MAIL_DOMAIN` | Mail Domain | nk-it.cloud |

### Netzwerk-Konfiguration

Die Bridge ist konfiguriert für:
- **Container IP**: ***********
- **IMAP Server**: ***********:1143
- **SMTP Server**: ***********:1025
- **Netzwerk**: *********/24

## 📧 Mail-Client Konfiguration

### Thunderbird

1. Neues Konto hinzufügen
2. Manuelle Konfiguration wählen
3. Einstellungen:
   - **IMAP Server**: ***********, Port 1143, Keine Verschlüsselung
   - **SMTP Server**: ***********, Port 1025, Keine Verschlüsselung
   - **Benutzername**: Deine PROTON E-Mail Adresse
   - **Passwort**: Bridge-Passwort (siehe .env)

### Outlook

1. Datei → Konto hinzufügen → Manuelle Einrichtung
2. IMAP/POP auswählen
3. Einstellungen wie bei Thunderbird

### Weitere Clients

Konfigurationsdateien verfügbar in:
- `config/thunderbird-config.xml`
- `config/outlook-settings.txt`

## 🧪 Tests

### Automatische Tests

```bash
# Verbindungstest
./tests/test-connection.sh

# Python Mail-Client Test
python3 ./tests/test-mail-client.py
```

### Manuelle Tests

```bash
# Netzwerk-Status prüfen
./scripts/monitor-network.sh

# Bridge-Status prüfen
systemctl status proton-bridge

# Logs prüfen
tail -f logs/bridge.log
```

## 🔧 Verwaltung

### Service-Befehle

```bash
# Bridge starten
./scripts/start-bridge.sh

# Bridge stoppen
sudo systemctl stop proton-bridge

# Bridge neu starten
sudo systemctl restart proton-bridge

# Status prüfen
sudo systemctl status proton-bridge
```

### Logs

```bash
# Bridge Logs
tail -f logs/bridge.log

# System Logs
journalctl -u proton-bridge -f

# Alle Logs
tail -f logs/*.log
```

### Backup

```bash
# Automatisches Backup bei Authentifizierung
# Manuelles Backup
cp -r data/keychain data/backup/manual_$(date +%Y%m%d_%H%M%S)
```

## 🔒 Sicherheit

### Firewall-Regeln

Die Installation konfiguriert automatisch UFW mit:
- SSH Zugriff erlaubt
- IMAP/SMTP nur für lokales Netzwerk (*********/24)
- Alle anderen eingehenden Verbindungen blockiert

### Berechtigungen

- `.env` Datei: 600 (nur Besitzer lesbar)
- Keychain Verzeichnis: 700 (nur Besitzer zugreifbar)
- SSL Zertifikate: 600/644

### SSL/TLS

- Selbstsignierte Zertifikate für lokale Verschlüsselung
- PROTON Verbindung über HTTPS/TLS

## 🐛 Fehlerbehebung

### Häufige Probleme

1. **Bridge startet nicht**
   ```bash
   # Logs prüfen
   tail -f logs/bridge.log
   
   # Prozess prüfen
   ps aux | grep protonmail-bridge
   ```

2. **Authentifizierung fehlgeschlagen**
   ```bash
   # Zugangsdaten prüfen
   ./config/auth-setup.sh
   
   # Erneut authentifizieren
   ./scripts/authenticate.sh
   ```

3. **Netzwerk-Probleme**
   ```bash
   # Netzwerk-Status prüfen
   ./scripts/monitor-network.sh
   
   # Firewall prüfen
   sudo ufw status verbose
   ```

4. **Mail-Client Verbindung fehlgeschlagen**
   ```bash
   # Ports testen
   nc -z *********** 1143
   nc -z *********** 1025
   
   # Bridge-Zugangsdaten prüfen
   grep BRIDGE_ .env
   ```

### Debug-Modus

```bash
# Debug-Modus aktivieren
echo "DEBUG_MODE=true" >> .env
echo "VERBOSE_LOGGING=true" >> .env

# Bridge mit Debug-Logs neu starten
./scripts/start-bridge.sh
```

## 📞 Support

### Log-Sammlung für Support

```bash
# Sammle alle relevanten Logs
tar -czf proton-bridge-logs-$(date +%Y%m%d).tar.gz logs/ config/ .env
```

### Nützliche Befehle

```bash
# System-Informationen
uname -a
cat /etc/os-release
docker --version
protonmail-bridge --version

# Netzwerk-Informationen
ip addr show
netstat -tlnp
ufw status verbose
```

## 📝 Changelog

### Version 1.0.0
- Initiale Einrichtung für nk-it.cloud
- LXC Container Konfiguration (***********)
- Automatische Installation und Tests
- Umfassende Dokumentation

## 📄 Lizenz

Dieses Setup ist für die interne Nutzung von nk-it.cloud bestimmt.
PROTON Mail Bridge unterliegt den Lizenzbedingungen von Proton Technologies AG.
