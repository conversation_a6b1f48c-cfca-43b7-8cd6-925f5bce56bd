#!/usr/bin/env python3
"""
PROTON Mail Bridge Client Test Script
====================================

Testet IMAP/SMTP Funktionalität mit Python Mail-Clients
"""

import os
import sys
import imaplib
import smtplib
import email
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import logging

# Setup Logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('/opt/mailbridge/logs/test-client.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProtonBridgeTest:
    def __init__(self):
        """Initialisiere Test-Klasse mit Konfiguration aus .env"""
        self.load_config()
        self.tests_passed = 0
        self.tests_failed = 0
    
    def load_config(self):
        """Lade Konfiguration aus .env Datei"""
        env_file = '/opt/mailbridge/.env'
        
        if not os.path.exists(env_file):
            logger.error("❌ .env Datei nicht gefunden")
            sys.exit(1)
        
        # Lade Umgebungsvariablen
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        
        # Setze Konfigurationsvariablen
        self.container_ip = os.getenv('CONTAINER_IP', '***********')
        self.imap_port = int(os.getenv('BRIDGE_IMAP_PORT', '1143'))
        self.smtp_port = int(os.getenv('BRIDGE_SMTP_PORT', '1025'))
        self.username = os.getenv('BRIDGE_USERNAME', '')
        self.password = os.getenv('BRIDGE_PASSWORD', '')
        self.mail_domain = os.getenv('MAIL_DOMAIN', 'nk-it.cloud')
        
        logger.info(f"Konfiguration geladen: {self.container_ip}:{self.imap_port}/{self.smtp_port}")
    
    def run_test(self, test_name, test_func):
        """Führe einen einzelnen Test aus"""
        logger.info(f"🔍 Test: {test_name}")
        
        try:
            result = test_func()
            if result:
                logger.info(f"✅ PASSED: {test_name}")
                self.tests_passed += 1
                return True
            else:
                logger.error(f"❌ FAILED: {test_name}")
                self.tests_failed += 1
                return False
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {str(e)}")
            self.tests_failed += 1
            return False
    
    def test_imap_connection(self):
        """Teste IMAP Verbindung"""
        try:
            # Verbinde zu IMAP Server
            imap = imaplib.IMAP4(self.container_ip, self.imap_port)
            
            # Login
            if self.username and self.password:
                imap.login(self.username, self.password)
                logger.info("IMAP Login erfolgreich")
            
            # Liste Mailboxen
            status, mailboxes = imap.list()
            if status == 'OK':
                logger.info(f"Gefundene Mailboxen: {len(mailboxes)}")
            
            # Schließe Verbindung
            imap.logout()
            return True
            
        except Exception as e:
            logger.error(f"IMAP Verbindung fehlgeschlagen: {str(e)}")
            return False
    
    def test_smtp_connection(self):
        """Teste SMTP Verbindung"""
        try:
            # Verbinde zu SMTP Server
            smtp = smtplib.SMTP(self.container_ip, self.smtp_port)
            
            # EHLO
            smtp.ehlo()
            
            # Login
            if self.username and self.password:
                smtp.login(self.username, self.password)
                logger.info("SMTP Login erfolgreich")
            
            # Schließe Verbindung
            smtp.quit()
            return True
            
        except Exception as e:
            logger.error(f"SMTP Verbindung fehlgeschlagen: {str(e)}")
            return False
    
    def test_send_email(self):
        """Teste E-Mail Versand"""
        if not self.username or not self.password:
            logger.warning("Keine Bridge-Zugangsdaten verfügbar, überspringe E-Mail Test")
            return True
        
        try:
            # Erstelle Test-E-Mail
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = self.username  # Sende an sich selbst
            msg['Subject'] = f"PROTON Bridge Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            body = f"""
Dies ist eine Test-E-Mail von der PROTON Mail Bridge.

Test Details:
- Gesendet am: {datetime.now()}
- Container IP: {self.container_ip}
- SMTP Port: {self.smtp_port}
- Domain: {self.mail_domain}

Wenn Sie diese E-Mail erhalten, funktioniert die Bridge korrekt!
"""
            msg.attach(MIMEText(body, 'plain'))
            
            # Sende E-Mail
            smtp = smtplib.SMTP(self.container_ip, self.smtp_port)
            smtp.ehlo()
            smtp.login(self.username, self.password)
            
            text = msg.as_string()
            smtp.sendmail(self.username, [self.username], text)
            smtp.quit()
            
            logger.info("Test-E-Mail erfolgreich gesendet")
            return True
            
        except Exception as e:
            logger.error(f"E-Mail Versand fehlgeschlagen: {str(e)}")
            return False
    
    def test_receive_email(self):
        """Teste E-Mail Empfang"""
        if not self.username or not self.password:
            logger.warning("Keine Bridge-Zugangsdaten verfügbar, überspringe Empfangs-Test")
            return True
        
        try:
            # Verbinde zu IMAP
            imap = imaplib.IMAP4(self.container_ip, self.imap_port)
            imap.login(self.username, self.password)
            
            # Wähle INBOX
            imap.select('INBOX')
            
            # Suche nach E-Mails
            status, messages = imap.search(None, 'ALL')
            
            if status == 'OK':
                message_ids = messages[0].split()
                logger.info(f"Gefundene E-Mails: {len(message_ids)}")
                
                # Hole die neueste E-Mail
                if message_ids:
                    latest_email_id = message_ids[-1]
                    status, msg_data = imap.fetch(latest_email_id, '(RFC822)')
                    
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        email_message = email.message_from_bytes(email_body)
                        
                        logger.info(f"Neueste E-Mail: {email_message['Subject']}")
                        logger.info(f"Von: {email_message['From']}")
                        logger.info(f"Datum: {email_message['Date']}")
            
            imap.logout()
            return True
            
        except Exception as e:
            logger.error(f"E-Mail Empfang fehlgeschlagen: {str(e)}")
            return False
    
    def test_ssl_connection(self):
        """Teste SSL/TLS Verbindung"""
        try:
            # Teste IMAP mit SSL
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # Versuche SSL Verbindung (falls unterstützt)
            try:
                imap_ssl = imaplib.IMAP4_SSL(self.container_ip, 993, ssl_context=context)
                imap_ssl.logout()
                logger.info("IMAP SSL Verbindung erfolgreich")
            except:
                logger.info("IMAP SSL nicht verfügbar (normal für Bridge)")
            
            return True
            
        except Exception as e:
            logger.error(f"SSL Test fehlgeschlagen: {str(e)}")
            return False
    
    def test_performance(self):
        """Teste Performance der Bridge"""
        import time
        
        try:
            # Teste IMAP Performance
            start_time = time.time()
            imap = imaplib.IMAP4(self.container_ip, self.imap_port)
            if self.username and self.password:
                imap.login(self.username, self.password)
            imap.logout()
            imap_time = (time.time() - start_time) * 1000
            
            # Teste SMTP Performance
            start_time = time.time()
            smtp = smtplib.SMTP(self.container_ip, self.smtp_port)
            smtp.ehlo()
            if self.username and self.password:
                smtp.login(self.username, self.password)
            smtp.quit()
            smtp_time = (time.time() - start_time) * 1000
            
            logger.info(f"IMAP Verbindungszeit: {imap_time:.2f}ms")
            logger.info(f"SMTP Verbindungszeit: {smtp_time:.2f}ms")
            
            # Performance ist OK wenn < 5000ms
            return imap_time < 5000 and smtp_time < 5000
            
        except Exception as e:
            logger.error(f"Performance Test fehlgeschlagen: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Führe alle Tests aus"""
        logger.info("🧪 Starte PROTON Mail Bridge Client Tests")
        logger.info("=" * 50)
        
        # Führe alle Tests aus
        self.run_test("IMAP Verbindung", self.test_imap_connection)
        self.run_test("SMTP Verbindung", self.test_smtp_connection)
        self.run_test("E-Mail Versand", self.test_send_email)
        self.run_test("E-Mail Empfang", self.test_receive_email)
        self.run_test("SSL Verbindung", self.test_ssl_connection)
        self.run_test("Performance", self.test_performance)
        
        # Test Zusammenfassung
        total_tests = self.tests_passed + self.tests_failed
        success_rate = (self.tests_passed * 100 // total_tests) if total_tests > 0 else 0
        
        logger.info("")
        logger.info("📊 Test Zusammenfassung:")
        logger.info(f"✅ Tests bestanden: {self.tests_passed}")
        logger.info(f"❌ Tests fehlgeschlagen: {self.tests_failed}")
        logger.info(f"📈 Erfolgsrate: {success_rate}%")
        
        if self.tests_failed == 0:
            logger.info("🎉 Alle Tests bestanden! Bridge ist einsatzbereit.")
            return True
        else:
            logger.warning("⚠️  Einige Tests fehlgeschlagen. Prüfe die Konfiguration.")
            return False

def main():
    """Hauptfunktion"""
    test_suite = ProtonBridgeTest()
    success = test_suite.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
