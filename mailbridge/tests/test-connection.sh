#!/bin/bash

# PROTON Mail Bridge Connection Tests
# ===================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Lade Umgebungsvariablen
if [[ -f "$PROJECT_DIR/.env" ]]; then
    source "$PROJECT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🧪 PROTON Mail Bridge Connection Tests"
echo "====================================="

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/test.log"
}

# Test Ergebnisse
TESTS_PASSED=0
TESTS_FAILED=0

# Test Funktion
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log "🔍 Test: $test_name"
    
    if eval "$test_command"; then
        log "✅ PASSED: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log "❌ FAILED: $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test 1: Bridge Prozess läuft
test_bridge_process() {
    pgrep -f "protonmail-bridge" > /dev/null
}

# Test 2: IMAP Port erreichbar
test_imap_port() {
    nc -z localhost "$BRIDGE_IMAP_PORT"
}

# Test 3: SMTP Port erreichbar
test_smtp_port() {
    nc -z localhost "$BRIDGE_SMTP_PORT"
}

# Test 4: IMAP Verbindung mit Authentifizierung
test_imap_auth() {
    if [[ -z "$BRIDGE_USERNAME" || -z "$BRIDGE_PASSWORD" ]]; then
        log "⚠️  Bridge Zugangsdaten nicht verfügbar, überspringe Auth-Test"
        return 0
    fi
    
    # Teste IMAP Login mit openssl
    echo -e "a001 LOGIN $BRIDGE_USERNAME $BRIDGE_PASSWORD\na002 LOGOUT" | \
    openssl s_client -connect localhost:$BRIDGE_IMAP_PORT -quiet 2>/dev/null | \
    grep -q "a001 OK"
}

# Test 5: SMTP Verbindung
test_smtp_connection() {
    # Teste SMTP Verbindung
    echo "QUIT" | nc localhost "$BRIDGE_SMTP_PORT" | grep -q "220"
}

# Test 6: Docker Container Status (falls verwendet)
test_docker_container() {
    if ! command -v docker &> /dev/null; then
        log "Docker nicht installiert, überspringe Container-Test"
        return 0
    fi
    
    if docker ps | grep -q "proton-bridge"; then
        return 0
    else
        log "Docker Container läuft nicht oder nicht gefunden"
        return 1
    fi
}

# Test 7: Netzwerk Erreichbarkeit von externer IP
test_external_access() {
    # Teste ob Bridge von der Container-IP erreichbar ist
    nc -z "$CONTAINER_IP" "$BRIDGE_IMAP_PORT" 2>/dev/null
}

# Test 8: SSL/TLS Zertifikat
test_ssl_certificate() {
    local cert_file="$PROJECT_DIR/config/certs/bridge.crt"
    
    if [[ -f "$cert_file" ]]; then
        # Prüfe ob Zertifikat gültig ist
        openssl x509 -in "$cert_file" -noout -checkend 86400 2>/dev/null
    else
        log "SSL Zertifikat nicht gefunden: $cert_file"
        return 1
    fi
}

# Test 9: Log Dateien
test_log_files() {
    local log_file="$PROJECT_DIR/logs/bridge.log"
    
    if [[ -f "$log_file" && -s "$log_file" ]]; then
        # Prüfe auf Fehler in den Logs
        if grep -q "ERROR\|FATAL" "$log_file"; then
            log "Fehler in Log-Datei gefunden"
            return 1
        fi
        return 0
    else
        log "Log-Datei nicht gefunden oder leer: $log_file"
        return 1
    fi
}

# Test 10: Mail-Versand Test
test_mail_sending() {
    if [[ -z "$BRIDGE_USERNAME" || -z "$BRIDGE_PASSWORD" ]]; then
        log "Bridge Zugangsdaten nicht verfügbar, überspringe Mail-Test"
        return 0
    fi
    
    # Erstelle Test-Mail
    local test_mail="/tmp/test_mail.txt"
    cat > "$test_mail" << EOF
From: $BRIDGE_USERNAME
To: $BRIDGE_USERNAME
Subject: PROTON Bridge Test Mail
Date: $(date -R)

Dies ist eine Test-Mail von der PROTON Mail Bridge.
Gesendet am: $(date)
Container IP: $CONTAINER_IP
EOF
    
    # Sende Test-Mail über SMTP
    if command -v sendmail &> /dev/null; then
        sendmail -S localhost:$BRIDGE_SMTP_PORT -f "$BRIDGE_USERNAME" "$BRIDGE_USERNAME" < "$test_mail"
        rm -f "$test_mail"
        return $?
    else
        log "sendmail nicht verfügbar, überspringe Mail-Versand-Test"
        return 0
    fi
}

# Performance Test
test_performance() {
    log "🚀 Performance Test..."
    
    # Teste IMAP Response Zeit
    local start_time=$(date +%s%N)
    nc -z localhost "$BRIDGE_IMAP_PORT"
    local end_time=$(date +%s%N)
    local imap_response_time=$(( (end_time - start_time) / 1000000 ))
    
    # Teste SMTP Response Zeit
    start_time=$(date +%s%N)
    nc -z localhost "$BRIDGE_SMTP_PORT"
    end_time=$(date +%s%N)
    local smtp_response_time=$(( (end_time - start_time) / 1000000 ))
    
    log "IMAP Response Zeit: ${imap_response_time}ms"
    log "SMTP Response Zeit: ${smtp_response_time}ms"
    
    # Performance ist OK wenn Response Zeit < 1000ms
    [[ $imap_response_time -lt 1000 && $smtp_response_time -lt 1000 ]]
}

# Haupttest-Suite
main() {
    log "Starte PROTON Mail Bridge Tests..."
    log "Container IP: $CONTAINER_IP"
    log "IMAP Port: $BRIDGE_IMAP_PORT"
    log "SMTP Port: $BRIDGE_SMTP_PORT"
    log ""
    
    # Führe alle Tests aus
    run_test "Bridge Prozess läuft" "test_bridge_process"
    run_test "IMAP Port erreichbar" "test_imap_port"
    run_test "SMTP Port erreichbar" "test_smtp_port"
    run_test "IMAP Authentifizierung" "test_imap_auth"
    run_test "SMTP Verbindung" "test_smtp_connection"
    run_test "Docker Container Status" "test_docker_container"
    run_test "Externe Netzwerk Erreichbarkeit" "test_external_access"
    run_test "SSL Zertifikat" "test_ssl_certificate"
    run_test "Log Dateien" "test_log_files"
    run_test "Mail Versand" "test_mail_sending"
    run_test "Performance" "test_performance"
    
    # Test Zusammenfassung
    log ""
    log "📊 Test Zusammenfassung:"
    log "✅ Tests bestanden: $TESTS_PASSED"
    log "❌ Tests fehlgeschlagen: $TESTS_FAILED"
    log "📈 Erfolgsrate: $(( TESTS_PASSED * 100 / (TESTS_PASSED + TESTS_FAILED) ))%"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log "🎉 Alle Tests bestanden! Bridge ist einsatzbereit."
        exit 0
    else
        log "⚠️  Einige Tests fehlgeschlagen. Prüfe die Konfiguration."
        exit 1
    fi
}

# Script ausführen
main "$@"
