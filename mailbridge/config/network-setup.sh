#!/bin/bash

# PROTON Mail Bridge Netzwerk-Konfiguration für LXC Container
# ===========================================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Lade Umgebungsvariablen
if [[ -f "$PROJECT_DIR/.env" ]]; then
    source "$PROJECT_DIR/.env"
else
    echo "❌ Fehler: .env Datei nicht gefunden"
    exit 1
fi

echo "🌐 PROTON Mail Bridge Netzwerk-Setup"
echo "===================================="
echo "Container IP: $CONTAINER_IP"
echo "IMAP Port: $BRIDGE_IMAP_PORT"
echo "SMTP Port: $BRIDGE_SMTP_PORT"
echo ""

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/network.log"
}

# Prüfe LXC Container Umgebung
check_lxc_environment() {
    log "Prüfe LXC Container Umgebung..."
    
    # Prüfe ob wir in einem LXC Container sind
    if [[ -f "/proc/1/environ" ]] && grep -q "container=lxc" /proc/1/environ; then
        log "✅ LXC Container erkannt"
    else
        log "⚠️  Warnung: Läuft möglicherweise nicht in einem LXC Container"
    fi
    
    # Prüfe verfügbare Netzwerk-Interfaces
    log "Verfügbare Netzwerk-Interfaces:"
    ip addr show | grep -E "^[0-9]+:" | tee -a "$PROJECT_DIR/logs/network.log"
}

# Konfiguriere Netzwerk-Interfaces
configure_network_interfaces() {
    log "Konfiguriere Netzwerk-Interfaces..."
    
    # Prüfe aktuelle IP-Konfiguration
    CURRENT_IP=$(ip route get 1 | awk '{print $7; exit}')
    log "Aktuelle IP: $CURRENT_IP"
    
    if [[ "$CURRENT_IP" != "$CONTAINER_IP" ]]; then
        log "⚠️  Warnung: Aktuelle IP ($CURRENT_IP) unterscheidet sich von konfigurierter IP ($CONTAINER_IP)"
        log "Möglicherweise ist eine manuelle Netzwerk-Konfiguration erforderlich"
    fi
    
    # Erstelle Netzwerk-Konfigurationsdatei für Netplan (Ubuntu/Debian)
    if command -v netplan &> /dev/null; then
        create_netplan_config
    fi
    
    # Erstelle Konfiguration für systemd-networkd
    create_systemd_network_config
}

# Erstelle Netplan Konfiguration
create_netplan_config() {
    log "Erstelle Netplan Konfiguration..."
    
    cat > "/tmp/99-proton-bridge.yaml" << EOF
network:
  version: 2
  ethernets:
    eth0:
      addresses:
        - $CONTAINER_IP/24
      gateway4: *********
      nameservers:
        addresses:
          - *******
          - *******
          - *******
EOF
    
    log "Netplan Konfiguration erstellt: /tmp/99-proton-bridge.yaml"
    log "Zum Anwenden: sudo cp /tmp/99-proton-bridge.yaml /etc/netplan/ && sudo netplan apply"
}

# Erstelle systemd-networkd Konfiguration
create_systemd_network_config() {
    log "Erstelle systemd-networkd Konfiguration..."
    
    cat > "/tmp/proton-bridge.network" << EOF
[Match]
Name=eth0

[Network]
Address=$CONTAINER_IP/24
Gateway=*********
DNS=*******
DNS=*******
DNS=*******

[Route]
Destination=0.0.0.0/0
Gateway=*********
EOF
    
    log "systemd-networkd Konfiguration erstellt: /tmp/proton-bridge.network"
    log "Zum Anwenden: sudo cp /tmp/proton-bridge.network /etc/systemd/network/"
}

# Konfiguriere Firewall
configure_firewall() {
    log "Konfiguriere Firewall..."
    
    # Installiere ufw falls nicht vorhanden
    if ! command -v ufw &> /dev/null; then
        log "Installiere ufw..."
        apt-get update && apt-get install -y ufw
    fi
    
    # Grundlegende Firewall-Regeln
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    
    # SSH Zugriff erlauben
    ufw allow ssh
    
    # PROTON Bridge Ports für lokales Netzwerk
    ufw allow from *********/24 to any port $BRIDGE_IMAP_PORT comment "PROTON Bridge IMAP"
    ufw allow from *********/24 to any port $BRIDGE_SMTP_PORT comment "PROTON Bridge SMTP"
    ufw allow from *********/24 to any port $BRIDGE_API_PORT comment "PROTON Bridge API"
    
    # Lokale Verbindungen erlauben
    ufw allow from 127.0.0.1
    ufw allow from ::1
    
    # Aktiviere Firewall
    ufw --force enable
    
    log "✅ Firewall konfiguriert"
    ufw status verbose | tee -a "$PROJECT_DIR/logs/network.log"
}

# Konfiguriere Port-Forwarding
configure_port_forwarding() {
    log "Konfiguriere Port-Forwarding..."
    
    # Erstelle iptables Regeln für Port-Forwarding
    cat > "/tmp/proton-bridge-iptables.sh" << EOF
#!/bin/bash

# PROTON Bridge iptables Regeln
# =============================

# Lösche existierende Regeln
iptables -t nat -F PREROUTING
iptables -t nat -F POSTROUTING

# Port-Forwarding für IMAP
iptables -t nat -A PREROUTING -p tcp --dport $BRIDGE_IMAP_PORT -j DNAT --to-destination $CONTAINER_IP:$BRIDGE_IMAP_PORT
iptables -t nat -A POSTROUTING -p tcp --dport $BRIDGE_IMAP_PORT -j MASQUERADE

# Port-Forwarding für SMTP
iptables -t nat -A PREROUTING -p tcp --dport $BRIDGE_SMTP_PORT -j DNAT --to-destination $CONTAINER_IP:$BRIDGE_SMTP_PORT
iptables -t nat -A POSTROUTING -p tcp --dport $BRIDGE_SMTP_PORT -j MASQUERADE

# Erlaube Forwarding
iptables -A FORWARD -p tcp --dport $BRIDGE_IMAP_PORT -j ACCEPT
iptables -A FORWARD -p tcp --dport $BRIDGE_SMTP_PORT -j ACCEPT

echo "iptables Regeln angewendet"
EOF
    
    chmod +x "/tmp/proton-bridge-iptables.sh"
    
    log "iptables Skript erstellt: /tmp/proton-bridge-iptables.sh"
}

# Teste Netzwerk-Konnektivität
test_network_connectivity() {
    log "Teste Netzwerk-Konnektivität..."
    
    # Teste lokale Verbindung
    if ping -c 3 127.0.0.1 &> /dev/null; then
        log "✅ Lokale Verbindung OK"
    else
        log "❌ Lokale Verbindung fehlgeschlagen"
    fi
    
    # Teste Gateway
    GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)
    if [[ -n "$GATEWAY" ]] && ping -c 3 "$GATEWAY" &> /dev/null; then
        log "✅ Gateway ($GATEWAY) erreichbar"
    else
        log "❌ Gateway nicht erreichbar"
    fi
    
    # Teste DNS
    if nslookup protonmail.com &> /dev/null; then
        log "✅ DNS Auflösung funktioniert"
    else
        log "❌ DNS Auflösung fehlgeschlagen"
    fi
    
    # Teste Internet-Verbindung
    if curl -s --connect-timeout 10 https://protonmail.com > /dev/null; then
        log "✅ Internet-Verbindung OK"
    else
        log "❌ Internet-Verbindung fehlgeschlagen"
    fi
}

# Erstelle Netzwerk-Monitoring Skript
create_monitoring_script() {
    log "Erstelle Netzwerk-Monitoring Skript..."
    
    cat > "$PROJECT_DIR/scripts/monitor-network.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Netzwerk-Monitoring
# =================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

source "$PROJECT_DIR/.env"

echo "📊 PROTON Bridge Netzwerk-Status"
echo "================================"

# Zeige aktuelle Netzwerk-Konfiguration
echo "Aktuelle IP-Konfiguration:"
ip addr show | grep -E "(inet |inet6 )"

echo ""
echo "Aktive Verbindungen:"
netstat -tlnp | grep -E "(1143|1025|8080)"

echo ""
echo "Firewall Status:"
ufw status verbose

echo ""
echo "Bridge Prozess Status:"
ps aux | grep protonmail-bridge | grep -v grep

echo ""
echo "Port-Tests:"
nc -z localhost $BRIDGE_IMAP_PORT && echo "✅ IMAP Port $BRIDGE_IMAP_PORT OK" || echo "❌ IMAP Port $BRIDGE_IMAP_PORT nicht erreichbar"
nc -z localhost $BRIDGE_SMTP_PORT && echo "✅ SMTP Port $BRIDGE_SMTP_PORT OK" || echo "❌ SMTP Port $BRIDGE_SMTP_PORT nicht erreichbar"
nc -z localhost $BRIDGE_API_PORT && echo "✅ API Port $BRIDGE_API_PORT OK" || echo "❌ API Port $BRIDGE_API_PORT nicht erreichbar"
EOF
    
    chmod +x "$PROJECT_DIR/scripts/monitor-network.sh"
    
    log "✅ Netzwerk-Monitoring Skript erstellt"
}

# Erstelle Mail-Client Konfiguration
create_mail_client_config() {
    log "Erstelle Mail-Client Konfigurationsdateien..."
    
    # Thunderbird Konfiguration
    cat > "$PROJECT_DIR/config/thunderbird-config.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<clientConfig version="1.1">
  <emailProvider id="nk-it.cloud">
    <domain>nk-it.cloud</domain>
    <displayName>nk-it.cloud via PROTON Bridge</displayName>
    <displayShortName>nk-it.cloud</displayShortName>
    <incomingServer type="imap">
      <hostname>$CONTAINER_IP</hostname>
      <port>$BRIDGE_IMAP_PORT</port>
      <socketType>plain</socketType>
      <authentication>password-cleartext</authentication>
      <username>%EMAILADDRESS%</username>
    </incomingServer>
    <outgoingServer type="smtp">
      <hostname>$CONTAINER_IP</hostname>
      <port>$BRIDGE_SMTP_PORT</port>
      <socketType>plain</socketType>
      <authentication>password-cleartext</authentication>
      <username>%EMAILADDRESS%</username>
    </outgoingServer>
  </emailProvider>
</clientConfig>
EOF
    
    # Outlook Konfiguration
    cat > "$PROJECT_DIR/config/outlook-settings.txt" << EOF
PROTON Bridge Outlook Konfiguration
===================================

Eingehende E-Mail (IMAP):
- Server: $CONTAINER_IP
- Port: $BRIDGE_IMAP_PORT
- Verschlüsselung: Keine
- Authentifizierung: Normales Passwort

Ausgehende E-Mail (SMTP):
- Server: $CONTAINER_IP
- Port: $BRIDGE_SMTP_PORT
- Verschlüsselung: Keine
- Authentifizierung: Normales Passwort

Benutzername: [Ihre PROTON E-Mail Adresse]
Passwort: [Bridge-Passwort aus .env Datei]
EOF
    
    log "✅ Mail-Client Konfigurationsdateien erstellt"
}

# Hauptfunktion
main() {
    log "Starte Netzwerk-Konfiguration..."
    
    check_lxc_environment
    configure_network_interfaces
    configure_firewall
    configure_port_forwarding
    test_network_connectivity
    create_monitoring_script
    create_mail_client_config
    
    log "🎉 Netzwerk-Konfiguration abgeschlossen!"
    log ""
    log "Wichtige Informationen:"
    log "Container IP: $CONTAINER_IP"
    log "IMAP Server: $CONTAINER_IP:$BRIDGE_IMAP_PORT"
    log "SMTP Server: $CONTAINER_IP:$BRIDGE_SMTP_PORT"
    log ""
    log "Nächste Schritte:"
    log "1. Prüfe Netzwerk: ./scripts/monitor-network.sh"
    log "2. Starte Bridge: ./scripts/start-bridge.sh"
    log "3. Teste Verbindung: ./tests/test-connection.sh"
    log ""
    log "Mail-Client Konfiguration:"
    log "- Thunderbird: $PROJECT_DIR/config/thunderbird-config.xml"
    log "- Outlook: $PROJECT_DIR/config/outlook-settings.txt"
}

# Script ausführen
main "$@"
