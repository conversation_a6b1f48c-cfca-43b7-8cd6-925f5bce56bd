#!/bin/bash

# PROTON Mail Bridge Authentifizierung Konfiguration
# ==================================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🔐 PROTON Mail Bridge Authentifizierung Setup"
echo "============================================="

# Funktion für Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$PROJECT_DIR/logs/auth-setup.log"
}

# Sichere Eingabe von Passwörtern
read_password() {
    local prompt="$1"
    local password=""
    
    echo -n "$prompt"
    while IFS= read -r -s -n1 char; do
        if [[ $char == $'\0' ]]; then
            break
        elif [[ $char == $'\177' ]]; then  # Backspace
            if [[ ${#password} -gt 0 ]]; then
                password="${password%?}"
                echo -ne '\b \b'
            fi
        else
            password+="$char"
            echo -n '*'
        fi
    done
    echo
    echo "$password"
}

# Validiere E-Mail Adresse
validate_email() {
    local email="$1"
    if [[ "$email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Sammle PROTON Zugangsdaten
collect_proton_credentials() {
    log "Sammle PROTON Mail Zugangsdaten..."
    
    # Username/E-Mail
    while true; do
        read -p "PROTON E-Mail Adresse (z.B. <EMAIL>): " PROTON_EMAIL
        if validate_email "$PROTON_EMAIL"; then
            break
        else
            echo "❌ Ungültige E-Mail Adresse. Bitte erneut eingeben."
        fi
    done
    
    # Password
    while true; do
        PROTON_PASSWORD=$(read_password "PROTON Passwort: ")
        if [[ ${#PROTON_PASSWORD} -ge 8 ]]; then
            break
        else
            echo "❌ Passwort muss mindestens 8 Zeichen lang sein."
        fi
    done
    
    # 2FA Code (falls aktiviert)
    read -p "2FA Code (falls aktiviert, sonst Enter): " PROTON_2FA
    
    log "✅ PROTON Zugangsdaten gesammelt"
}

# Erstelle sichere .env Datei
create_secure_env() {
    log "Erstelle sichere .env Datei..."
    
    local env_file="$PROJECT_DIR/.env"
    local env_backup="$PROJECT_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Backup der existierenden .env
    if [[ -f "$env_file" ]]; then
        cp "$env_file" "$env_backup"
        log "Backup erstellt: $env_backup"
    fi
    
    # Aktualisiere .env mit neuen Zugangsdaten
    sed -i "s/^PROTON_USERNAME=.*/PROTON_USERNAME=$PROTON_EMAIL/" "$env_file"
    sed -i "s/^PROTON_PASSWORD=.*/PROTON_PASSWORD=$PROTON_PASSWORD/" "$env_file"
    
    # Setze sichere Berechtigungen
    chmod 600 "$env_file"
    chown $(whoami):$(whoami) "$env_file"
    
    log "✅ .env Datei aktualisiert und gesichert"
}

# Erstelle Keychain Verzeichnis
setup_keychain() {
    log "Richte Keychain ein..."
    
    local keychain_dir="$PROJECT_DIR/data/keychain"
    mkdir -p "$keychain_dir"
    
    # Setze sichere Berechtigungen
    chmod 700 "$keychain_dir"
    
    # Erstelle Keychain Konfiguration
    cat > "$keychain_dir/config.json" << EOF
{
    "version": "1.0",
    "created": "$(date -Iseconds)",
    "container_ip": "***********",
    "domain": "nk-it.cloud",
    "encryption": "AES-256"
}
EOF
    
    chmod 600 "$keychain_dir/config.json"
    
    log "✅ Keychain eingerichtet"
}

# Generiere Bridge-spezifische Konfiguration
generate_bridge_config() {
    log "Generiere Bridge-Konfiguration..."
    
    # Erstelle Bridge-spezifische Konfigurationsdatei
    cat > "$PROJECT_DIR/config/bridge-auth.conf" << EOF
# PROTON Bridge Authentifizierung
# Generiert am: $(date)

[authentication]
method = proton
domain = nk-it.cloud
auto_login = true
save_credentials = true

[security]
encryption = true
keychain_backend = file
keychain_path = $PROJECT_DIR/data/keychain

[network]
bind_ip = 0.0.0.0
external_ip = ***********
imap_port = 1143
smtp_port = 1025

[logging]
auth_log = $PROJECT_DIR/logs/auth.log
level = info
EOF
    
    chmod 600 "$PROJECT_DIR/config/bridge-auth.conf"
    
    log "✅ Bridge-Konfiguration generiert"
}

# Teste PROTON Verbindung
test_proton_connection() {
    log "Teste PROTON Verbindung..."
    
    # Teste Verbindung zu PROTON Servern
    if ping -c 3 protonmail.com &> /dev/null; then
        log "✅ Verbindung zu PROTON Servern erfolgreich"
    else
        log "❌ Keine Verbindung zu PROTON Servern"
        return 1
    fi
    
    # Teste HTTPS Verbindung
    if curl -s --connect-timeout 10 https://protonmail.com > /dev/null; then
        log "✅ HTTPS Verbindung zu PROTON erfolgreich"
    else
        log "❌ HTTPS Verbindung zu PROTON fehlgeschlagen"
        return 1
    fi
    
    return 0
}

# Erstelle Authentifizierungs-Skript
create_auth_script() {
    log "Erstelle Authentifizierungs-Skript..."
    
    cat > "$PROJECT_DIR/scripts/authenticate.sh" << 'EOF'
#!/bin/bash

# PROTON Bridge Authentifizierung
# ===============================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

source "$PROJECT_DIR/.env"

echo "🔐 Starte PROTON Bridge Authentifizierung..."

# Starte Bridge im CLI Modus
protonmail-bridge --cli << BRIDGE_EOF
login
$PROTON_USERNAME
$PROTON_PASSWORD
info
list
BRIDGE_EOF

echo "✅ Authentifizierung abgeschlossen"
EOF
    
    chmod +x "$PROJECT_DIR/scripts/authenticate.sh"
    
    log "✅ Authentifizierungs-Skript erstellt"
}

# Erstelle Systemd Service (optional)
create_systemd_service() {
    log "Erstelle Systemd Service..."
    
    cat > "/tmp/proton-bridge.service" << EOF
[Unit]
Description=PROTON Mail Bridge for nk-it.cloud
After=network.target
Wants=network.target

[Service]
Type=simple
User=$(whoami)
Group=$(whoami)
WorkingDirectory=$PROJECT_DIR
Environment=HOME=$HOME
EnvironmentFile=$PROJECT_DIR/.env
ExecStart=/usr/local/bin/protonmail-bridge --noninteractive --log-level=info
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    log "Systemd Service Datei erstellt: /tmp/proton-bridge.service"
    log "Zum Installieren: sudo cp /tmp/proton-bridge.service /etc/systemd/system/"
    log "Dann: sudo systemctl enable proton-bridge && sudo systemctl start proton-bridge"
}

# Hauptfunktion
main() {
    log "Starte Authentifizierung Setup..."
    
    collect_proton_credentials
    create_secure_env
    setup_keychain
    generate_bridge_config
    test_proton_connection
    create_auth_script
    create_systemd_service
    
    log "🎉 Authentifizierung Setup abgeschlossen!"
    log ""
    log "Nächste Schritte:"
    log "1. Installiere Bridge: ./scripts/install-bridge.sh"
    log "2. Starte Bridge: ./scripts/start-bridge.sh"
    log "3. Authentifiziere: ./scripts/authenticate.sh"
    log "4. Teste Verbindung: ./tests/test-connection.sh"
    log ""
    log "Wichtige Dateien:"
    log "- Konfiguration: $PROJECT_DIR/.env"
    log "- Keychain: $PROJECT_DIR/data/keychain/"
    log "- Logs: $PROJECT_DIR/logs/"
}

# Script ausführen
main "$@"
