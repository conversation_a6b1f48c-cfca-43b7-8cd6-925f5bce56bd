#!/bin/bash

# PROTON Bridge Backup
# ====================

BACKUP_DIR="./data/backup/manual_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "💾 Erstelle Backup in: $BACKUP_DIR"

# Backup wichtiger Dateien
cp .env "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  .env nicht gefunden"
cp -r config/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  config/ nicht gefunden"
cp -r data/keychain/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  keychain/ nicht gefunden"
cp -r logs/ "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  logs/ nicht gefunden"

# Erstelle Backup-Info
cat > "$BACKUP_DIR/backup-info.txt" << BACKUP_EOF
PROTON Bridge Backup
===================
Erstellt: $(date)
Hostname: $(hostname)
Container IP: ***********
Domain: nk-it.cloud

Enthaltene Dateien:
- .env (Konfiguration)
- config/ (Konfigurationsdateien)
- data/keychain/ (Schlüssel)
- logs/ (Log-Dateien)
BACKUP_EOF

echo "✅ Backup erstellt: $BACKUP_DIR"
echo "📁 Backup-Größe: $(du -sh "$BACKUP_DIR" | cut -f1)"
