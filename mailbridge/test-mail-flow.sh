#!/bin/bash

# PMG → PROTON Bridge Mail-Flow Test
# ==================================

# Konfiguration
PMG_IP="***********"
PMG_SMTP_PORT="25"
BRIDGE_IP="***********"
FROM_EMAIL="<EMAIL>"
TO_EMAIL="<EMAIL>"
TEST_SUBJECT="PMG Mail-Flow Test $(date '+%Y-%m-%d %H:%M:%S')"

echo "📧 PMG → PROTON Bridge Mail-Flow Test"
echo "====================================="
echo "Von: $FROM_EMAIL"
echo "An: $TO_EMAIL"
echo "PMG: $PMG_IP:$PMG_SMTP_PORT"
echo "Bridge: $BRIDGE_IP"
echo ""

# Test-Mail erstellen
create_test_mail() {
    local mail_file="/tmp/test_mail_$(date +%s).txt"
    
    cat > "$mail_file" << EOF
From: $FROM_EMAIL
To: $TO_EMAIL
Subject: $TEST_SUBJECT
Date: $(date -R)
Message-ID: <test-$(date +%s)@nk-it.cloud>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

PMG → PROTON Bridge Mail-Flow Test
==================================

Diese Test-Mail wurde gesendet um den Mail-Flow zu testen:

Zeitstempel: $(date)
Von: $FROM_EMAIL
An: $TO_EMAIL

Mail-Flow:
1. Test-Skript → PMG ($PMG_IP:$PMG_SMTP_PORT)
2. PMG → PROTON Bridge ($BRIDGE_IP:1025)
3. PROTON Bridge → PROTON Mail Server
4. PROTON Mail → Posteingang

Wenn Sie diese E-Mail erhalten, funktioniert der komplette Mail-Flow!

Test-Details:
- PMG IP: $PMG_IP
- Bridge IP: $BRIDGE_IP
- Domain: nk-it.cloud
- Gesendet von: $(hostname)
- Test-ID: test-$(date +%s)

---
Automatischer Test von PMG Mail Gateway Integration
EOF
    
    echo "$mail_file"
}

# SMTP Test mit netcat
test_smtp_netcat() {
    echo "🔧 Test 1: SMTP mit netcat"
    echo "=========================="
    
    local mail_file=$(create_test_mail)
    
    # SMTP Befehle vorbereiten
    local smtp_commands="/tmp/smtp_commands_$(date +%s).txt"
    cat > "$smtp_commands" << EOF
HELO nk-it.cloud
MAIL FROM:<$FROM_EMAIL>
RCPT TO:<$TO_EMAIL>
DATA
$(cat "$mail_file")
.
QUIT
EOF
    
    echo "Sende Mail über PMG SMTP..."
    if nc "$PMG_IP" "$PMG_SMTP_PORT" < "$smtp_commands"; then
        echo "✅ Mail über PMG gesendet"
    else
        echo "❌ Fehler beim Senden über PMG"
    fi
    
    # Cleanup
    rm -f "$mail_file" "$smtp_commands"
}

# SMTP Test mit sendmail (falls verfügbar)
test_smtp_sendmail() {
    echo ""
    echo "🔧 Test 2: SMTP mit sendmail"
    echo "============================"
    
    if ! command -v sendmail &> /dev/null; then
        echo "⚠️  sendmail nicht verfügbar, überspringe Test"
        return
    fi
    
    local mail_file=$(create_test_mail)
    
    echo "Sende Mail mit sendmail über PMG..."
    if sendmail -S "$PMG_IP:$PMG_SMTP_PORT" -f "$FROM_EMAIL" "$TO_EMAIL" < "$mail_file"; then
        echo "✅ Mail mit sendmail gesendet"
    else
        echo "❌ Fehler beim Senden mit sendmail"
    fi
    
    rm -f "$mail_file"
}

# SMTP Test mit curl
test_smtp_curl() {
    echo ""
    echo "🔧 Test 3: SMTP mit curl"
    echo "========================"
    
    local mail_file=$(create_test_mail)
    
    echo "Sende Mail mit curl über PMG..."
    if curl -s --url "smtp://$PMG_IP:$PMG_SMTP_PORT" \
            --mail-from "$FROM_EMAIL" \
            --mail-rcpt "$TO_EMAIL" \
            --upload-file "$mail_file"; then
        echo "✅ Mail mit curl gesendet"
    else
        echo "❌ Fehler beim Senden mit curl"
    fi
    
    rm -f "$mail_file"
}

# Python SMTP Test
test_smtp_python() {
    echo ""
    echo "🔧 Test 4: SMTP mit Python"
    echo "=========================="
    
    if ! command -v python3 &> /dev/null; then
        echo "⚠️  Python3 nicht verfügbar, überspringe Test"
        return
    fi
    
    python3 << EOF
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

# Mail erstellen
msg = MIMEMultipart()
msg['From'] = '$FROM_EMAIL'
msg['To'] = '$TO_EMAIL'
msg['Subject'] = '$TEST_SUBJECT (Python)'

body = """
PMG → PROTON Bridge Mail-Flow Test (Python)
==========================================

Diese Test-Mail wurde mit Python über PMG gesendet.

Zeitstempel: """ + str(datetime.now()) + """
Von: $FROM_EMAIL
An: $TO_EMAIL

Mail-Flow:
Python → PMG ($PMG_IP:$PMG_SMTP_PORT) → Bridge → PROTON

Test erfolgreich!
"""

msg.attach(MIMEText(body, 'plain'))

try:
    # SMTP Verbindung
    server = smtplib.SMTP('$PMG_IP', $PMG_SMTP_PORT)
    server.set_debuglevel(0)  # Debug aus
    
    # Mail senden
    text = msg.as_string()
    server.sendmail('$FROM_EMAIL', '$TO_EMAIL', text)
    server.quit()
    
    print("✅ Mail mit Python gesendet")
except Exception as e:
    print(f"❌ Python SMTP Fehler: {e}")
EOF
}

# Verbindungstests
test_connections() {
    echo ""
    echo "🌐 Verbindungstests"
    echo "=================="
    
    # PMG SMTP Test
    echo "PMG SMTP ($PMG_IP:$PMG_SMTP_PORT):"
    if nc -z "$PMG_IP" "$PMG_SMTP_PORT"; then
        echo "✅ PMG SMTP erreichbar"
    else
        echo "❌ PMG SMTP nicht erreichbar"
    fi
    
    # Bridge SMTP Test
    echo ""
    echo "Bridge SMTP ($BRIDGE_IP:1025):"
    if nc -z "$BRIDGE_IP" 1025; then
        echo "✅ Bridge SMTP erreichbar"
    else
        echo "❌ Bridge SMTP nicht erreichbar"
    fi
    
    # Bridge IMAP Test
    echo ""
    echo "Bridge IMAP ($BRIDGE_IP:1143):"
    if nc -z "$BRIDGE_IP" 1143; then
        echo "✅ Bridge IMAP erreichbar"
    else
        echo "❌ Bridge IMAP nicht erreichbar"
    fi
}

# PMG SSH Verbindung und Settings Check
check_pmg_settings() {
    echo ""
    echo "� PMG Settings Check via SSH"
    echo "============================="

    if ! command -v ssh &> /dev/null; then
        echo "⚠️  SSH nicht verfügbar"
        return
    fi

    echo "Verbinde zu PMG via SSH..."
    echo "PMG IP: $PMG_IP"
    echo ""

    # SSH Verbindung zu PMG
    ssh -o ConnectTimeout=10 root@"$PMG_IP" << 'EOF'
echo "🔗 Verbunden mit PMG: $(hostname)"
echo "=================================="

echo ""
echo "📋 PMG System Info:"
echo "Hostname: $(hostname)"
echo "IP: $(hostname -I)"
echo "PMG Version: $(pmgversion 2>/dev/null || echo "PMG Version nicht verfügbar")"
echo "Uptime: $(uptime)"

echo ""
echo "🌐 Netzwerk Konnektivität zu Bridge:"
echo "Bridge IP: ***********"
if nc -z *********** 1025; then
    echo "✅ Bridge SMTP (1025) erreichbar von PMG"
else
    echo "❌ Bridge SMTP (1025) NICHT erreichbar von PMG"
fi

if nc -z *********** 1143; then
    echo "✅ Bridge IMAP (1143) erreichbar von PMG"
else
    echo "❌ Bridge IMAP (1143) NICHT erreichbar von PMG"
fi

echo ""
echo "📧 PMG Mail Configuration:"
echo "=========================="

echo "Transport Maps:"
if [ -f /etc/pmg/transport ]; then
    echo "Transport Datei gefunden:"
    cat /etc/pmg/transport | grep -v "^#" | grep -v "^$" || echo "Keine Transports konfiguriert"
else
    echo "❌ Transport Datei nicht gefunden"
fi

echo ""
echo "Relay Domains:"
if [ -f /etc/pmg/domains ]; then
    echo "Domains Datei gefunden:"
    cat /etc/pmg/domains | grep -v "^#" | grep -v "^$" || echo "Keine Relay Domains konfiguriert"
else
    echo "❌ Domains Datei nicht gefunden"
fi

echo ""
echo "My Networks:"
if [ -f /etc/pmg/mynetworks ]; then
    echo "MyNetworks Datei gefunden:"
    cat /etc/pmg/mynetworks | grep -v "^#" | grep -v "^$" || echo "Keine Networks konfiguriert"
else
    echo "❌ MyNetworks Datei nicht gefunden"
fi

echo ""
echo "📝 Aktuelle Mail Logs (letzte 10 Zeilen):"
echo "========================================"
tail -10 /var/log/mail.log 2>/dev/null || echo "Mail.log nicht verfügbar"

echo ""
echo "🔍 PMG Postfix Main Config (relevante Teile):"
echo "============================================="
if [ -f /etc/postfix/main.cf ]; then
    echo "Relay Host:"
    grep "^relayhost" /etc/postfix/main.cf || echo "Kein relayhost konfiguriert"
    echo ""
    echo "Transport Maps:"
    grep "^transport_maps" /etc/postfix/main.cf || echo "Keine transport_maps konfiguriert"
    echo ""
    echo "My Networks:"
    grep "^mynetworks" /etc/postfix/main.cf || echo "Keine mynetworks konfiguriert"
else
    echo "❌ Postfix main.cf nicht gefunden"
fi

echo ""
echo "🧪 Test: Direkte Bridge Verbindung von PMG"
echo "=========================================="
echo "Teste SMTP Verbindung zu Bridge..."
{
    echo "HELO pmg.local.nk-it.cloud"
    sleep 1
    echo "MAIL FROM:<<EMAIL>>"
    sleep 1
    echo "RCPT TO:<<EMAIL>>"
    sleep 1
    echo "DATA"
    sleep 1
    echo "From: <EMAIL>"
    echo "To: <EMAIL>"
    echo "Subject: PMG Direct Bridge Test"
    echo ""
    echo "Direct test from PMG to Bridge"
    echo "."
    sleep 1
    echo "QUIT"
} | nc *********** 1025

echo ""
echo "✅ PMG Settings Check abgeschlossen"
echo "Verbindung wird beendet..."
EOF

    echo ""
    echo "🔚 SSH Verbindung zu PMG beendet"
}

# PMG Logs anzeigen (Fallback)
show_pmg_logs() {
    echo ""
    echo "📝 PMG Mail-Logs (Fallback)"
    echo "==========================="
    echo "⚠️  Verwende check_pmg_settings() für detaillierte Logs via SSH"
}

# Monitoring während Test
monitor_mail_flow() {
    echo ""
    echo "📊 Mail-Flow Monitoring"
    echo "======================"
    
    echo "Überwache Verbindungen während Mail-Versand..."
    
    # Zeige aktive Verbindungen zu PMG
    echo "Aktive Verbindungen zu PMG:"
    netstat -an | grep ":$PMG_SMTP_PORT" || echo "Keine aktiven PMG Verbindungen"
    
    echo ""
    echo "Aktive Verbindungen zu Bridge:"
    netstat -an | grep ":1025\|:1143" || echo "Keine aktiven Bridge Verbindungen"
}

# Hauptfunktion
main() {
    echo "Starte umfassenden Mail-Flow Test..."
    echo ""
    
    # Verbindungen testen
    test_connections
    
    # Monitoring starten
    monitor_mail_flow
    
    # Verschiedene SMTP Tests
    test_smtp_netcat
    test_smtp_curl
    test_smtp_python
    
    # Optional: sendmail test
    test_smtp_sendmail
    
    # PMG Logs anzeigen
    show_pmg_logs
    
    echo ""
    echo "🎉 Mail-Flow Tests abgeschlossen!"
    echo ""
    echo "📋 Zusammenfassung:"
    echo "=================="
    echo "✉️  Test-Mails gesendet an: $TO_EMAIL"
    echo "📤 Von Adresse: $FROM_EMAIL"
    echo "🔗 Mail-Flow: Test → PMG ($PMG_IP) → Bridge ($BRIDGE_IP) → PROTON"
    echo ""
    echo "📧 Prüfen Sie Ihren Posteingang auf Test-Mails!"
    echo "🌐 PMG Web-Interface: https://$PMG_IP:8006"
    echo ""
    echo "💡 Tipp: Führen Sie 'watch -n 5 netstat -an | grep :25' aus"
    echo "   um Live-Verbindungen zu PMG zu überwachen"
}

# Script ausführen
main "$@"
