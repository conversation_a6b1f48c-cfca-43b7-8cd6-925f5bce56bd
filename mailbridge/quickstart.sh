#!/bin/bash

# PROTON Bridge Quick-Start für nk-it.cloud
# =========================================

echo "🚀 PROTON Bridge Quick-Start"
echo "============================"

# Prüfe ob bereits installiert
if command -v protonmail-bridge &> /dev/null; then
    echo "✅ PROTON Bridge bereits installiert"
else
    echo "📥 Installiere PROTON Bridge..."
    sudo ./scripts/install-bridge.sh
fi

# Prüfe .env Konfiguration
if [[ -f ".env" ]] && grep -q "PROTON_USERNAME=your-username" .env; then
    echo "🔐 Richte Authentifizierung ein..."
    ./config/auth-setup.sh
fi

# Konfiguriere Netzwerk
echo "🌐 Konfiguriere Netzwerk..."
sudo ./config/network-setup.sh

# Starte Service
echo "🚀 Starte Bridge Service..."
if command -v "docker compose" &> /dev/null; then
    docker compose up -d proton-bridge
else
    ./scripts/start-bridge.sh
fi

# Führe Tests durch
echo "🧪 Führe Tests durch..."
./tests/test-connection.sh

echo ""
echo "🎉 Quick-Start abgeschlossen!"
echo "Prüfe die Logs für Details: tail -f logs/*.log"
