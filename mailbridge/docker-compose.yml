version: '3.8'

services:
  proton-bridge:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge
    restart: unless-stopped
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
    
    ports:
      - "${CONTAINER_IP}:${BRIDGE_IMAP_PORT}:1143"  # IMAP
      - "${CONTAINER_IP}:${BRIDGE_SMTP_PORT}:1025"  # SMTP
      - "${CONTAINER_IP}:${BRIDGE_API_PORT}:8080"   # Bridge API
    
    volumes:
      - ./data:/root/.cache/protonmail/bridge
      - ./config:/root/.config/protonmail-bridge
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro
    
    networks:
      - mailbridge-network
    
    healthcheck:
      test: ["<PERSON><PERSON>", "nc", "-z", "localhost", "1143"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Mail monitoring service
  mailhog:
    image: mailhog/mailhog:latest
    container_name: mailhog
    restart: unless-stopped
    ports:
      - "${CONTAINER_IP}:8025:8025"  # Web UI
      - "${CONTAINER_IP}:1025:1025"  # SMTP (alternative)
    networks:
      - mailbridge-network
    profiles:
      - monitoring

networks:
  mailbridge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  bridge-data:
    driver: local
  bridge-config:
    driver: local
