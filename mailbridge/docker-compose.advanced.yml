# PROTON Mail Bridge - Er<PERSON><PERSON><PERSON> Docker Compose Konfiguration
# =============================================================
# Verschiedene Implementierungsoptionen für unterschiedliche Anforderungen

version: '3.8'

services:
  # Option 1: Offizielle deb-basierte Version (Empfohlen für amd64)
  proton-bridge-deb:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge-deb
    restart: unless-stopped
    profiles:
      - deb
      - default
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
    
    ports:
      - "${CONTAINER_IP}:143:143"     # IMAP
      - "${CONTAINER_IP}:25:25"       # SMTP
      - "${CONTAINER_IP}:8080:8080"   # Bridge API
    
    volumes:
      - protonmail-deb:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro
    
    networks:
      - mailbridge-network
    
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "143"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    security_opt:
      - no-new-privileges:true
    read_only: false

  # Option 2: Build-basierte Version (Multi-Arch: amd64, arm64, arm/v7)
  proton-bridge-build:
    image: shenxn/protonmail-bridge:build
    container_name: proton-bridge-build
    restart: unless-stopped
    profiles:
      - build
      - arm
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
    
    ports:
      - "${CONTAINER_IP}:143:143"
      - "${CONTAINER_IP}:25:25"
      - "${CONTAINER_IP}:8080:8080"
    
    volumes:
      - protonmail-build:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro
    
    networks:
      - mailbridge-network
    
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "143"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Option 3: Localhost-only Version (Sicherheit)
  proton-bridge-localhost:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge-localhost
    restart: unless-stopped
    profiles:
      - localhost
      - secure
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
    
    ports:
      - "127.0.0.1:143:143"   # Nur localhost
      - "127.0.0.1:25:25"     # Nur localhost
      - "127.0.0.1:8080:8080" # Nur localhost
    
    volumes:
      - protonmail-localhost:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro
    
    networks:
      - mailbridge-network

  # Option 4: SMTP-only Version (nur für Versand)
  proton-bridge-smtp-only:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge-smtp-only
    restart: unless-stopped
    profiles:
      - smtp-only
      - notification
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
    
    ports:
      - "${CONTAINER_IP}:25:25"       # Nur SMTP
      - "${CONTAINER_IP}:8080:8080"   # Bridge API
    
    volumes:
      - protonmail-smtp:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro
    
    networks:
      - mailbridge-network

  # Option 5: Load Balancer Setup (Hochverfügbarkeit)
  proton-bridge-primary:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge-primary
    restart: unless-stopped
    profiles:
      - ha
      - loadbalancer
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
      - BRIDGE_INSTANCE=primary
    
    ports:
      - "${CONTAINER_IP}:1143:143"    # IMAP Primary
      - "${CONTAINER_IP}:1025:25"     # SMTP Primary
    
    volumes:
      - protonmail-primary:/root
      - ./logs:/var/log/protonmail-bridge
    
    networks:
      - mailbridge-network

  proton-bridge-secondary:
    image: shenxn/protonmail-bridge:latest
    container_name: proton-bridge-secondary
    restart: unless-stopped
    profiles:
      - ha
      - loadbalancer
    
    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}
      - BRIDGE_INSTANCE=secondary
    
    ports:
      - "${CONTAINER_IP}:2143:143"    # IMAP Secondary
      - "${CONTAINER_IP}:2025:25"     # SMTP Secondary
    
    volumes:
      - protonmail-secondary:/root
      - ./logs:/var/log/protonmail-bridge
    
    networks:
      - mailbridge-network

  # Load Balancer (HAProxy)
  loadbalancer:
    image: haproxy:latest
    container_name: proton-loadbalancer
    restart: unless-stopped
    profiles:
      - ha
      - loadbalancer
    
    ports:
      - "${CONTAINER_IP}:143:143"     # Load Balanced IMAP
      - "${CONTAINER_IP}:25:25"       # Load Balanced SMTP
      - "${CONTAINER_IP}:8404:8404"   # HAProxy Stats
    
    volumes:
      - ./config/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    
    networks:
      - mailbridge-network
    
    depends_on:
      - proton-bridge-primary
      - proton-bridge-secondary

  # Monitoring mit Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: proton-prometheus
    restart: unless-stopped
    profiles:
      - monitoring
    
    ports:
      - "${CONTAINER_IP}:9090:9090"
    
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    networks:
      - mailbridge-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: proton-grafana
    restart: unless-stopped
    profiles:
      - monitoring
    
    ports:
      - "${CONTAINER_IP}:3000:3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    
    networks:
      - mailbridge-network

  # Mail Testing Service
  mailhog:
    image: mailhog/mailhog:latest
    container_name: proton-mailhog
    restart: unless-stopped
    profiles:
      - testing
      - monitoring
    
    ports:
      - "${CONTAINER_IP}:8025:8025"   # Web UI
      - "${CONTAINER_IP}:1025:1025"   # SMTP Test
    
    networks:
      - mailbridge-network

networks:
  mailbridge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  protonmail-deb:
    driver: local
  protonmail-build:
    driver: local
  protonmail-localhost:
    driver: local
  protonmail-smtp:
    driver: local
  protonmail-primary:
    driver: local
  protonmail-secondary:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
